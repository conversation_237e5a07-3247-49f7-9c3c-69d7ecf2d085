#!/usr/bin/env python3
"""
Simple test script that doesn't require complex imports.
Tests basic JWT generation with the PureStorage keys.
"""

import sys
import os
from pathlib import Path

def test_keys_exist():
    """Test that the SSL keys exist."""
    print("🔍 Checking SSL keys...")
    
    private_key = Path("private.pem")
    public_key = Path("public.pem")
    
    if not private_key.exists():
        print("❌ private.pem not found")
        return False
    
    if not public_key.exists():
        print("❌ public.pem not found")
        return False
    
    print("✅ Both SSL keys found")
    return True

def test_key_format():
    """Test that the keys are in the correct format."""
    print("🔍 Checking key formats...")
    
    try:
        with open("private.pem", "r") as f:
            private_content = f.read()
        
        with open("public.pem", "r") as f:
            public_content = f.read()
        
        if "BEGIN" not in private_content or "PRIVATE KEY" not in private_content:
            print("❌ private.pem doesn't appear to be a valid private key")
            return False
        
        if "BEGIN PUBLIC KEY" not in public_content:
            print("❌ public.pem doesn't appear to be a valid public key")
            return False
        
        print("✅ Key formats look correct")
        return True
        
    except Exception as e:
        print(f"❌ Error reading keys: {e}")
        return False

def test_config():
    """Test that the config file exists and has the OTLP endpoint."""
    print("🔍 Checking configuration...")
    
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("❌ config.yaml not found")
        return False
    
    try:
        with open("config.yaml", "r") as f:
            config_content = f.read()
        
        if "grafana.net/otlp" in config_content:
            print("✅ Grafana Cloud OTLP endpoint configured")
        elif "otlp" in config_content:
            print("✅ OTLP endpoint configured")
        else:
            print("⚠️  OTLP configuration not found in config.yaml")
            return False
        
        if "authorization" in config_content:
            print("✅ Authorization header configured")
        else:
            print("⚠️  No authorization header found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def test_dependencies():
    """Test if we can import the required modules."""
    print("🔍 Checking Python dependencies...")
    
    required_modules = [
        ("cryptography", "Cryptography library for SSL keys"),
        ("jwt", "PyJWT for token generation"),
        ("requests", "HTTP requests library"),
        ("yaml", "YAML configuration parsing"),
        ("opentelemetry", "OpenTelemetry for OTLP")
    ]
    
    missing = []
    for module, description in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} (MISSING)")
            missing.append(module)
    
    if missing:
        print(f"\n📦 To install missing dependencies:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🧪 PureStorage Health Monitor - Quick Test")
    print("=" * 50)
    
    tests = [
        ("SSL Keys", test_keys_exist),
        ("Key Format", test_key_format),
        ("Configuration", test_config),
        ("Dependencies", test_dependencies)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to run the monitor.")
        print("\n🚀 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Test authentication: python -m src.main --test-auth")
        print("3. Run single collection: python -m src.main --run-once")
        print("4. Start monitoring: python -m src.main")
        return True
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
