version: '3.8'

services:
  purestorage-monitor:
    build: .
    container_name: purestorage-health-monitor
    restart: unless-stopped
    volumes:
      - ./config.yaml:/app/config.yaml:ro
      - ./private.pem:/app/private.pem:ro
      - ./public.pem:/app/public.pem:ro
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      # Override OTLP endpoint via environment if needed
      - OTLP__ENDPOINT=${OTLP_ENDPOINT:-http://localhost:4317}
      - OTLP__HEADERS=${OTLP_HEADERS:-{}}
    command: ["python", "-m", "src.main", "--config", "/app/config.yaml"]
    networks:
      - monitoring

  # Optional: OTEL Collector for local testing
  # Uncomment if you want to run a local OTLP collector
  # otel-collector:
  #   image: otel/opentelemetry-collector-contrib:latest
  #   container_name: otel-collector
  #   restart: unless-stopped
  #   ports:
  #     - "4317:4317"   # OTLP gRPC receiver
  #     - "4318:4318"   # OTLP HTTP receiver
  #     - "8888:8888"   # Prometheus metrics
  #     - "8889:8889"   # Prometheus exporter metrics
  #   volumes:
  #     - ./otel-collector-config.yaml:/etc/otelcol-contrib/otel-collector-config.yaml:ro
  #   command: ["--config=/etc/otelcol-contrib/otel-collector-config.yaml"]
  #   networks:
  #     - monitoring

networks:
  monitoring:
    driver: bridge
