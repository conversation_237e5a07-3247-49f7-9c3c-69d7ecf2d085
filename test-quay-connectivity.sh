#!/bin/bash

# Test script to debug Quay registry connectivity issues

echo "🔍 Quay Registry Connectivity Test"
echo "=================================="

QUAY_REGISTRY="quay.education.nsw.gov.au"
PROXY_HOST="proxy.det.nsw.edu.au"
PROXY_PORT="80"
PROXY_URL="http://${PROXY_HOST}:${PROXY_PORT}"

echo ""
echo "Testing registry: $QUAY_REGISTRY"
echo "Testing proxy: $PROXY_URL"

# Test 1: Basic DNS resolution
echo ""
echo "Test 1: DNS resolution for Quay registry"
if nslookup "$QUAY_REGISTRY" >/dev/null 2>&1; then
    echo "✅ DNS resolution for $QUAY_REGISTRY works"
    echo "   IP: $(nslookup "$QUAY_REGISTRY" | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null || echo "Could not extract IP")"
else
    echo "❌ DNS resolution for $QUAY_REGISTRY failed"
    echo "   This could be a DNS configuration issue"
fi

# Test 2: Basic connectivity to Quay
echo ""
echo "Test 2: Basic connectivity to Quay registry"
if ping -c 1 "$QUAY_REGISTRY" >/dev/null 2>&1; then
    echo "✅ Quay registry $QUAY_REGISTRY is reachable via ping"
else
    echo "❌ Quay registry $QUAY_REGISTRY is NOT reachable via ping"
    echo "   This could indicate network restrictions or firewall rules"
fi

# Test 3: HTTPS connectivity (direct)
echo ""
echo "Test 3: HTTPS connectivity (direct connection)"
if command -v curl >/dev/null 2>&1; then
    echo "Testing direct HTTPS connection..."
    if curl -I --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" 2>/dev/null | head -1; then
        echo "✅ Direct HTTPS connection to Quay registry works"
    else
        echo "❌ Direct HTTPS connection to Quay registry failed"
        echo "   Error details:"
        curl -I --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" 2>&1 | head -5
    fi
else
    echo "⚠️  curl not available, skipping HTTPS test"
fi

# Test 4: HTTPS connectivity via proxy
echo ""
echo "Test 4: HTTPS connectivity via proxy"
if command -v curl >/dev/null 2>&1; then
    if ping -c 1 "$PROXY_HOST" >/dev/null 2>&1; then
        echo "Testing HTTPS connection via proxy..."
        if curl -I --proxy "$PROXY_URL" --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" 2>/dev/null | head -1; then
            echo "✅ HTTPS connection via proxy works"
        else
            echo "❌ HTTPS connection via proxy failed"
            echo "   Error details:"
            curl -I --proxy "$PROXY_URL" --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" 2>&1 | head -5
        fi
    else
        echo "❌ Proxy host $PROXY_HOST is not reachable, skipping proxy test"
    fi
else
    echo "⚠️  curl not available, skipping proxy test"
fi

# Test 5: Docker connectivity test
echo ""
echo "Test 5: Docker connectivity test"
if command -v docker >/dev/null 2>&1; then
    echo "Testing Docker connectivity to registry..."
    
    # Test without credentials first
    if timeout 30 docker pull hello-world >/dev/null 2>&1; then
        echo "✅ Docker can pull from public registries"
    else
        echo "❌ Docker cannot pull from public registries"
        echo "   This suggests Docker networking issues"
    fi
    
    # Test registry connectivity
    echo "Testing registry connectivity with Docker..."
    if timeout 30 bash -c "docker pull alpine:latest >/dev/null 2>&1"; then
        echo "✅ Docker networking appears to be working"
        
        # Now test the specific registry
        echo "Testing specific registry access..."
        # This will fail with auth error, but should not timeout
        docker_output=$(timeout 30 docker pull "$QUAY_REGISTRY/test/nonexistent:latest" 2>&1 || true)
        if echo "$docker_output" | grep -q "unauthorized\|authentication\|denied"; then
            echo "✅ Registry is reachable (authentication error is expected)"
        elif echo "$docker_output" | grep -q "timeout\|connection"; then
            echo "❌ Registry connection timed out or failed"
            echo "   Output: $docker_output"
        else
            echo "⚠️  Unexpected response: $docker_output"
        fi
    else
        echo "❌ Docker networking issues detected"
    fi
else
    echo "⚠️  Docker not available, skipping Docker test"
fi

# Test 6: Network route analysis
echo ""
echo "Test 6: Network route analysis"
if command -v traceroute >/dev/null 2>&1; then
    echo "Tracing route to $QUAY_REGISTRY (first 5 hops):"
    timeout 30 traceroute "$QUAY_REGISTRY" 2>/dev/null | head -7 || echo "Traceroute failed or timed out"
elif command -v tracert >/dev/null 2>&1; then
    echo "Tracing route to $QUAY_REGISTRY (first 5 hops):"
    timeout 30 tracert "$QUAY_REGISTRY" 2>/dev/null | head -7 || echo "Tracert failed or timed out"
else
    echo "⚠️  traceroute/tracert not available, skipping route analysis"
fi

# Test 7: Port connectivity
echo ""
echo "Test 7: Port connectivity"
if command -v nc >/dev/null 2>&1; then
    echo "Testing HTTPS port (443) connectivity..."
    if timeout 10 nc -z "$QUAY_REGISTRY" 443 2>/dev/null; then
        echo "✅ Port 443 is accessible on $QUAY_REGISTRY"
    else
        echo "❌ Port 443 is NOT accessible on $QUAY_REGISTRY"
    fi
else
    echo "⚠️  netcat (nc) not available, skipping port test"
fi

echo ""
echo "🔧 Diagnosis and Recommendations:"
echo "================================="

# Provide recommendations based on test results
if ! nslookup "$QUAY_REGISTRY" >/dev/null 2>&1; then
    echo "❌ DNS Issue: Cannot resolve $QUAY_REGISTRY"
    echo "   Solutions:"
    echo "   1. Check DNS configuration"
    echo "   2. Verify you're on the correct network"
    echo "   3. Try using a different DNS server"
elif ! ping -c 1 "$QUAY_REGISTRY" >/dev/null 2>&1; then
    echo "❌ Network Issue: Registry is not reachable"
    echo "   Solutions:"
    echo "   1. Check firewall rules"
    echo "   2. Verify network connectivity"
    echo "   3. Check if VPN is required"
elif ! curl -I --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" >/dev/null 2>&1; then
    if ping -c 1 "$PROXY_HOST" >/dev/null 2>&1 && curl -I --proxy "$PROXY_URL" --connect-timeout 30 --max-time 60 "https://$QUAY_REGISTRY/v2/" >/dev/null 2>&1; then
        echo "✅ Registry accessible via proxy"
        echo "   Recommendation: Ensure Docker is configured to use proxy"
    else
        echo "❌ HTTPS connectivity issue"
        echo "   Solutions:"
        echo "   1. Check if HTTPS traffic is blocked"
        echo "   2. Verify SSL/TLS configuration"
        echo "   3. Check corporate firewall rules"
    fi
else
    echo "✅ Basic connectivity looks good"
    echo "   The timeout issue might be Docker-specific"
    echo "   Recommendations:"
    echo "   1. Check Docker daemon configuration"
    echo "   2. Verify Docker proxy settings"
    echo "   3. Check Docker registry authentication"
fi

echo ""
echo "🚀 For Bitbucket Pipeline:"
echo "========================="
echo "The updated pipeline includes:"
echo "1. Automatic proxy detection and configuration"
echo "2. Connectivity testing before Docker operations"
echo "3. Timeout handling for Docker commands"
echo "4. Better error reporting"
echo ""
echo "If issues persist, consider:"
echo "1. Using a different registry (if available)"
echo "2. Configuring pipeline to run on different runners"
echo "3. Setting up registry mirrors or caching"
echo ""
