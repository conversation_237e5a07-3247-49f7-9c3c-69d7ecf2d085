#!/bin/bash

# PureStorage Health Monitor - Nomad Deployment Script

set -e

NOMAD_JOB_FILE="purestorage-monitor.nomad"
DOCKER_IMAGE="purestorage-monitor:latest"

echo "🚀 PureStorage Health Monitor - Nomad Deployment"
echo "================================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists nomad; then
    echo "❌ Error: Nomad CLI not found!"
    echo "Please install Nomad: https://www.nomadproject.io/downloads"
    exit 1
fi

if ! command_exists docker; then
    echo "❌ Error: Docker not found!"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command_exists consul; then
    echo "⚠️  Warning: Consul CLI not found. You may need it for key/value storage."
fi

echo "✅ Prerequisites check passed!"

# Build Docker image
echo ""
echo "🔨 Building Docker image..."
if [ -f "Dockerfile" ]; then
    docker build -t "$DOCKER_IMAGE" .
    echo "✅ Docker image built successfully!"
else
    echo "❌ Error: Dockerfile not found!"
    echo "Please ensure you're running this script from the project root directory."
    exit 1
fi

# Check if Nomad job file exists
if [ ! -f "$NOMAD_JOB_FILE" ]; then
    echo "❌ Error: $NOMAD_JOB_FILE not found!"
    exit 1
fi

# Validate Nomad job
echo ""
echo "🔍 Validating Nomad job specification..."
if nomad job validate "$NOMAD_JOB_FILE"; then
    echo "✅ Nomad job validation passed!"
else
    echo "❌ Nomad job validation failed!"
    exit 1
fi

# Check Consul KV requirements
echo ""
echo "🔑 Checking Consul KV requirements..."
echo "The following keys need to be set in Consul KV:"
echo "  - purestorage/app_id"
echo "  - purestorage/private_key_passphrase"
echo "  - purestorage/otlp_endpoint"
echo "  - purestorage/otlp_username"
echo "  - purestorage/otlp_password"
echo "  - purestorage/ssl/private_key"
echo "  - purestorage/ssl/public_key"
echo ""

read -p "Have you configured all required Consul KV pairs? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Please configure the required Consul KV pairs before deploying."
    echo ""
    echo "Example commands:"
    echo "  consul kv put purestorage/app_id 'pure1:apikey:your-app-id'"
    echo "  consul kv put purestorage/private_key_passphrase 'your-passphrase'"
    echo "  consul kv put purestorage/otlp_endpoint 'https://your-otlp-endpoint:4317'"
    echo "  consul kv put purestorage/otlp_username 'your-username'"
    echo "  consul kv put purestorage/otlp_password 'your-password'"
    echo "  consul kv put purestorage/ssl/private_key @private.pem"
    echo "  consul kv put purestorage/ssl/public_key @public.pem"
    exit 1
fi

# Deploy the job
echo ""
echo "🚀 Deploying to Nomad..."
if nomad job run "$NOMAD_JOB_FILE"; then
    echo "✅ Job deployed successfully!"
else
    echo "❌ Job deployment failed!"
    exit 1
fi

# Show job status
echo ""
echo "📊 Job Status:"
nomad job status purestorage-monitor

echo ""
echo "✅ Deployment completed!"
echo ""
echo "📋 Useful commands:"
echo "  View job status:    nomad job status purestorage-monitor"
echo "  View allocations:   nomad job allocs purestorage-monitor"
echo "  View logs:          nomad alloc logs -f <alloc-id>"
echo "  Stop job:           nomad job stop purestorage-monitor"
echo "  Restart job:        nomad job restart purestorage-monitor"
echo ""
