#!/bin/bash

# PureStorage Health Monitor - Pipeline Validation Script
# This script validates the pipeline configuration and prerequisites

set -e

echo "🔍 PureStorage Health Monitor - Pipeline Validation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    
    case $status in
        "OK")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "ℹ️  $message"
            ;;
    esac
}

# Validation results
ERRORS=0
WARNINGS=0

echo ""
echo "📋 Checking Pipeline Configuration Files..."

# Check if bitbucket-pipelines.yml exists
if [ -f "bitbucket-pipelines.yml" ]; then
    print_status "OK" "bitbucket-pipelines.yml found"
    
    # Basic YAML syntax check (if yq is available)
    if command -v yq >/dev/null 2>&1; then
        if yq eval . bitbucket-pipelines.yml >/dev/null 2>&1; then
            print_status "OK" "bitbucket-pipelines.yml syntax is valid"
        else
            print_status "ERROR" "bitbucket-pipelines.yml has syntax errors"
            ERRORS=$((ERRORS + 1))
        fi
    else
        print_status "WARN" "yq not available, skipping YAML syntax validation"
        WARNINGS=$((WARNINGS + 1))
    fi
else
    print_status "ERROR" "bitbucket-pipelines.yml not found"
    ERRORS=$((ERRORS + 1))
fi

# Check deployment scripts
if [ -f "deploy-pipeline.sh" ]; then
    print_status "OK" "deploy-pipeline.sh found"
    if [ -x "deploy-pipeline.sh" ]; then
        print_status "OK" "deploy-pipeline.sh is executable"
    else
        print_status "WARN" "deploy-pipeline.sh is not executable"
        WARNINGS=$((WARNINGS + 1))
    fi
else
    print_status "ERROR" "deploy-pipeline.sh not found"
    ERRORS=$((ERRORS + 1))
fi

# Check Nomad job files
if [ -f "purestorage-monitor.nomad" ]; then
    print_status "OK" "purestorage-monitor.nomad found"
else
    print_status "ERROR" "purestorage-monitor.nomad not found"
    ERRORS=$((ERRORS + 1))
fi

# Check Dockerfile
if [ -f "Dockerfile" ]; then
    print_status "OK" "Dockerfile found"
else
    print_status "ERROR" "Dockerfile not found"
    ERRORS=$((ERRORS + 1))
fi

# Check requirements.txt
if [ -f "requirements.txt" ]; then
    print_status "OK" "requirements.txt found"
else
    print_status "ERROR" "requirements.txt not found"
    ERRORS=$((ERRORS + 1))
fi

# Check source code structure
echo ""
echo "📁 Checking Source Code Structure..."

if [ -d "src" ]; then
    print_status "OK" "src directory found"
    
    # Check main Python files
    for file in "main.py" "config.py" "jwt_generator.py" "oauth_client.py" "purestorage_client.py" "reporters.py"; do
        if [ -f "src/$file" ]; then
            print_status "OK" "src/$file found"
        else
            print_status "ERROR" "src/$file not found"
            ERRORS=$((ERRORS + 1))
        fi
    done
else
    print_status "ERROR" "src directory not found"
    ERRORS=$((ERRORS + 1))
fi

# Check documentation
echo ""
echo "📚 Checking Documentation..."

for doc in "README.md" "NOMAD-DEPLOYMENT.md" "BITBUCKET-PIPELINE.md"; do
    if [ -f "$doc" ]; then
        print_status "OK" "$doc found"
    else
        print_status "WARN" "$doc not found"
        WARNINGS=$((WARNINGS + 1))
    fi
done

# Check environment variables (if running in Bitbucket)
echo ""
echo "🔧 Checking Environment Variables..."

if [ -n "$BITBUCKET_REPO_SLUG" ]; then
    print_status "INFO" "Running in Bitbucket Pipelines environment"
    
    # Check required variables
    for var in "QUAY_USERNAME" "QUAY_PASSWORD" "QUAY_NAMESPACE" "NOMAD_ADDR"; do
        if [ -n "${!var}" ]; then
            print_status "OK" "$var is set"
        else
            print_status "ERROR" "$var is not set"
            ERRORS=$((ERRORS + 1))
        fi
    done
    
    # Check optional variables
    for var in "CONSUL_HTTP_ADDR" "ENVIRONMENT"; do
        if [ -n "${!var}" ]; then
            print_status "OK" "$var is set"
        else
            print_status "WARN" "$var is not set (optional)"
            WARNINGS=$((WARNINGS + 1))
        fi
    done
else
    print_status "INFO" "Not running in Bitbucket Pipelines environment"
    print_status "INFO" "Skipping environment variable checks"
fi

# Summary
echo ""
echo "📊 Validation Summary"
echo "===================="

if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    print_status "OK" "All validations passed!"
    echo ""
    echo "🚀 Your pipeline is ready for deployment!"
elif [ $ERRORS -eq 0 ]; then
    print_status "WARN" "Validation completed with $WARNINGS warning(s)"
    echo ""
    echo "⚠️  Please review the warnings above, but your pipeline should work."
else
    print_status "ERROR" "Validation failed with $ERRORS error(s) and $WARNINGS warning(s)"
    echo ""
    echo "❌ Please fix the errors above before using the pipeline."
    exit 1
fi

echo ""
echo "📋 Next Steps:"
echo "1. Commit and push your changes to trigger the pipeline"
echo "2. Configure Bitbucket repository variables (see BITBUCKET-PIPELINE.md)"
echo "3. Set up Consul KV pairs using ./setup-consul-kv.sh"
echo "4. Monitor your first pipeline run in Bitbucket"
echo ""

exit 0
