purestorage:
  app_id: pure1:apikey:cajETP0O7YI55zQx
  private_key_path: private.pem
  public_key_path: public.pem
  private_key_passphrase: pure1
  api_base_url: https://api.pure1.purestorage.com
  token_expiry_days: 30
  proxy_url: http://proxy.det.nsw.edu.au:80

otlp:
  enabled: true
  endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp
  headers: {}
  username: "533612"
  password: "****************************************************************************************************************************************************************"
  timeout: 30
  service_name: purestorage-health-monitor

log_reporting:
  enabled: false
  level: INFO

monitoring:
  collection_interval: 300
  max_alerts: 1000
  max_arrays: 100
  max_hardware: 500
  max_drives: 1000
  max_controllers: 200
  max_volumes: 1500
  alert_severities:
  - critical
  - warning
  alert_states:
  - open
  metrics_enabled: true
  metrics_resolution: 300000
  metrics_history_hours: 24

logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file: purestorage_monitor.log
  max_size: 10485760
  backup_count: 5
