# PureStorage Health Monitor

A comprehensive health monitoring solution for PureStorage arrays that collects health data via the Pure1 API and reports metrics to Mimi<PERSON> and logs to <PERSON>.

## Features

- **JWT Authentication**: Secure authentication using SSL keys and JWT tokens with configurable expiry
- **OAuth 2.0 Token Exchange**: Automatic token exchange for API access
- **Comprehensive Health Data Collection**: 
  - Array status and capacity
  - Alerts (critical, warning, info)
  - Hardware components status
  - Drive health
  - Controller status
  - Volume information
- **OTLP Reporting**: Send metrics and traces to any OTLP-compatible endpoint
- **Structured Logging**: Optional structured log reporting for alerts and health data
- **Configurable Monitoring**: Flexible collection intervals and data limits
- **Docker Support**: Lightweight containerized deployment

## Prerequisites

- PureStorage Pure1 API credentials (SSL keys and App ID)
- OTLP-compatible observability backend (e.g., Grafana Cloud, Datadog, New Relic, etc.)
- Docker and Docker Compose (for containerized deployment)
- Python 3.11+ (for local development)

## Quick Start

### 1. Setup Credentials

Place your PureStorage SSL keys in the project directory:
- `private.pem` - Your private key
- `public.pem` - Your public key

### 2. Create Configuration

Generate a sample configuration file:

```bash
python -m src.main --create-config
```

Edit `config.yaml` with your specific settings:

```yaml
purestorage:
  app_id: "pure1:apikey:cajETP0O7YI55zQx"  # Your PureStorage App ID
  private_key_path: "private.pem"
  public_key_path: "public.pem"

otlp:
  enabled: true
  endpoint: "https://your-otlp-endpoint:4317"  # Your OTLP endpoint
  headers:
    authorization: "Bearer your-token"  # If authentication required
  service_name: "purestorage-health-monitor"
```

### 3. Deploy with Docker

```bash
# Start the monitor
docker-compose up -d

# View logs
docker-compose logs -f purestorage-monitor

# Stop services
docker-compose down
```

### 4. Monitor Your Data

Your metrics and traces will be sent to your configured OTLP endpoint. Check your observability platform for:
- PureStorage health metrics
- Trace data showing collection operations
- Alert information

## Configuration

### PureStorage Settings

```yaml
purestorage:
  app_id: "pure1:apikey:your-app-id"
  private_key_path: "private.pem"
  public_key_path: "public.pem"
  private_key_passphrase: null  # If your key is encrypted
  api_base_url: "https://api.pure1.purestorage.com"
  token_expiry_days: 30
```

### Monitoring Settings

```yaml
monitoring:
  collection_interval: 300  # seconds
  max_alerts: 1000
  max_arrays: 100
  alert_severities: ["critical", "warning"]
  alert_states: ["open"]
  metrics_enabled: true
```

### OTLP Reporting Settings

```yaml
otlp:
  enabled: true
  endpoint: "https://your-otlp-endpoint:4317"
  headers:
    authorization: "Bearer your-api-key"
  timeout: 30
  service_name: "purestorage-health-monitor"

log_reporting:
  enabled: false  # Enable for additional structured logging
  level: "INFO"
```

## Usage

### Test Authentication

```bash
python -m src.main --test-auth
```

### Run Single Collection

```bash
python -m src.main --run-once
```

### Continuous Monitoring

```bash
python -m src.main
```

### Docker Deployment

```bash
# Build and start
docker-compose up --build -d

# Scale monitoring (if needed)
docker-compose up --scale purestorage-monitor=2 -d
```

## Metrics

The following metrics are exported via OTLP:

- `purestorage_array_status` - Array health status (1=healthy, 0=unhealthy)
- `purestorage_array_capacity_total_bytes` - Total array capacity in bytes
- `purestorage_array_capacity_used_bytes` - Used array capacity in bytes
- `purestorage_alerts_total` - Alert counts by severity and state
- `purestorage_hardware_status` - Hardware component status (1=healthy, 0=unhealthy)
- `purestorage_drive_status` - Drive health status (1=healthy, 0=unhealthy)
- `purestorage_controller_status` - Controller status (1=healthy, 0=unhealthy)

All metrics include relevant labels for filtering and grouping (array_name, array_id, severity, etc.).

## Traces

Trace data is sent via OTLP showing:

- **Collection Operations**: Spans for each data collection cycle
- **API Calls**: Individual API request traces
- **Health Summary**: Overall collection summary with attributes

## Logs

Optional structured logs can be enabled for:

- **Alerts**: Individual alert details with metadata
- **Health Summary**: Periodic health summaries with component counts

## Development

### Local Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
python -m src.jwt_generator
python -m src.oauth_client
python -m src.purestorage_client
```

### Project Structure

```
├── src/
│   ├── __init__.py
│   ├── main.py              # Main application
│   ├── config.py            # Configuration management
│   ├── jwt_generator.py     # JWT token generation
│   ├── oauth_client.py      # OAuth 2.0 token exchange
│   ├── purestorage_client.py # PureStorage API client
│   └── reporters.py         # Mimir/Loki reporters
├── docker-compose.yml       # Docker deployment
├── Dockerfile              # Container definition
├── requirements.txt        # Python dependencies
├── config.yaml            # Configuration file
├── mimir-config.yaml      # Mimir configuration
├── loki-config.yaml       # Loki configuration
├── private.pem            # Your private key
└── public.pem             # Your public key
```

## Troubleshooting

### Authentication Issues

1. Verify your SSL keys are correct and accessible
2. Check that your App ID matches your PureStorage registration
3. Ensure private key passphrase is correct (if encrypted)

### API Rate Limits

The PureStorage API has rate limits. Monitor the logs for rate limit warnings and adjust collection intervals if needed.

### Docker Issues

```bash
# Check container logs
docker-compose logs purestorage-monitor

# Restart services
docker-compose restart

# Rebuild containers
docker-compose up --build --force-recreate
```

### Network Connectivity

Ensure the container can reach:
- `api.pure1.purestorage.com` (PureStorage API)
- Internal services (Mimir, Loki)

## Security Considerations

- Store SSL keys securely and limit access
- Use encrypted private keys when possible
- Rotate JWT tokens regularly (30-day default)
- Monitor API access logs
- Use secure networks for deployment

## License

This project is provided as-is for monitoring PureStorage infrastructure health.
