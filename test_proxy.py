#!/usr/bin/env python3
"""
Test script to verify proxy configuration is working correctly.
"""

import requests
import sys
from src.config import load_config

def test_proxy_connectivity():
    """Test proxy connectivity to external services."""
    
    # Load configuration
    try:
        config = load_config("config.yaml")
        proxy_url = config.purestorage.proxy_url
        print(f"Testing proxy configuration: {proxy_url}")
    except Exception as e:
        print(f"Failed to load config: {e}")
        return False
    
    if not proxy_url:
        print("No proxy URL configured")
        return False
    
    # Configure session with proxy
    session = requests.Session()
    session.proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    session.verify = False  # Disable SSL verification for corporate proxy
    
    # Test URLs
    test_urls = [
        "https://api.pure1.purestorage.com",
        "https://httpbin.org/ip",  # Simple test endpoint
        "http://httpbin.org/ip"    # HTTP test
    ]
    
    results = []
    
    for url in test_urls:
        try:
            print(f"\nTesting connection to: {url}")
            print(f"Using proxy: {proxy_url}")
            
            response = session.get(url, timeout=10)
            print(f"Status: {response.status_code}")
            
            if "httpbin.org/ip" in url:
                # Show the IP address returned (should be proxy IP)
                try:
                    ip_data = response.json()
                    print(f"External IP seen: {ip_data.get('origin', 'Unknown')}")
                except:
                    print("Could not parse IP response")
            
            results.append((url, True, response.status_code))
            
        except requests.exceptions.RequestException as e:
            print(f"Failed to connect: {e}")
            results.append((url, False, str(e)))
    
    # Summary
    print("\n" + "="*50)
    print("PROXY TEST SUMMARY")
    print("="*50)
    
    success_count = 0
    for url, success, result in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{status}: {url}")
        if success:
            success_count += 1
        else:
            print(f"   Error: {result}")
    
    print(f"\nResults: {success_count}/{len(results)} tests passed")
    
    if success_count == len(results):
        print("🎉 All proxy tests passed! Configuration looks good.")
        return True
    else:
        print("⚠️  Some tests failed. Check proxy configuration.")
        return False

if __name__ == "__main__":
    success = test_proxy_connectivity()
    sys.exit(0 if success else 1)
