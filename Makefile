# PureStorage Health Monitor Makefile

.PHONY: help install test run docker-build docker-up docker-down clean config

help:
	@echo "Available commands:"
	@echo "  install      - Install Python dependencies"
	@echo "  test         - Run component tests"
	@echo "  run          - Run the monitor locally"
	@echo "  run-once     - Run a single collection cycle"
	@echo "  test-auth    - Test authentication"
	@echo "  config       - Create sample configuration"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-up    - Start Docker services"
	@echo "  docker-down  - Stop Docker services"
	@echo "  docker-logs  - View Docker logs"
	@echo "  clean        - Clean up generated files"

install:
	pip install -r requirements.txt

test:
	@echo "Testing JWT generator..."
	python -m src.jwt_generator
	@echo "Testing OAuth client..."
	python -m src.oauth_client
	@echo "Testing PureStorage client..."
	python -m src.purestorage_client
	@echo "Testing reporters..."
	python -m src.reporters
	@echo "Testing configuration..."
	python -m src.config

run:
	python -m src.main

run-once:
	python -m src.main --run-once

test-auth:
	python -m src.main --test-auth

config:
	python -m src.main --create-config

docker-build:
	docker-compose build

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

docker-logs:
	docker-compose logs -f

docker-restart:
	docker-compose restart

docker-clean:
	docker-compose down -v
	docker system prune -f

clean:
	rm -f *.log
	rm -rf __pycache__
	rm -rf src/__pycache__
	rm -rf .pytest_cache
	find . -name "*.pyc" -delete

# Development targets
dev-setup: install config
	@echo "Development environment setup complete"
	@echo "1. Edit config.yaml with your PureStorage credentials"
	@echo "2. Place your private.pem and public.pem files in the project root"
	@echo "3. Run 'make test-auth' to verify authentication"
	@echo "4. Run 'make run-once' for a test collection"

# Production targets
prod-deploy: docker-build docker-up
	@echo "Production deployment started"
	@echo "Monitor is sending data to your configured OTLP endpoint"
	@echo "Check your observability platform for PureStorage metrics and traces"

# Monitoring targets
status:
	docker-compose ps

logs-monitor:
	docker-compose logs -f purestorage-monitor
