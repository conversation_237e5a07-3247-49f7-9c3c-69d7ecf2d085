#!/usr/bin/env python3
"""
Test script to verify the PureStorage Health Monitor setup.
"""

import sys
import os
from pathlib import Path

def test_dependencies():
    """Test that all required dependencies are available."""
    print("Testing dependencies...")
    
    required_modules = [
        'cryptography',
        'jwt',
        'requests',
        'pydantic',
        'pydantic_settings',
        'schedule',
        'prometheus_client',
        'yaml'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✓ {module}")
        except ImportError:
            print(f"  ✗ {module}")
            missing.append(module)
    
    if missing:
        print(f"\nMissing dependencies: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("All dependencies available!")
    return True

def test_ssl_keys():
    """Test that SSL keys are present."""
    print("\nTesting SSL keys...")
    
    private_key = Path("private.pem")
    public_key = Path("public.pem")
    
    if private_key.exists():
        print("  ✓ private.pem found")
    else:
        print("  ✗ private.pem not found")
        return False
    
    if public_key.exists():
        print("  ✓ public.pem found")
    else:
        print("  ✗ public.pem not found")
        return False
    
    # Test key format
    try:
        with open(private_key, 'r') as f:
            content = f.read()
            if "BEGIN" in content and "PRIVATE KEY" in content:
                print("  ✓ private.pem appears to be a valid key file")
            else:
                print("  ✗ private.pem doesn't appear to be a valid key file")
                return False
    except Exception as e:
        print(f"  ✗ Error reading private.pem: {e}")
        return False
    
    try:
        with open(public_key, 'r') as f:
            content = f.read()
            if "BEGIN PUBLIC KEY" in content:
                print("  ✓ public.pem appears to be a valid key file")
            else:
                print("  ✗ public.pem doesn't appear to be a valid key file")
                return False
    except Exception as e:
        print(f"  ✗ Error reading public.pem: {e}")
        return False
    
    print("SSL keys are present and valid!")
    return True

def test_imports():
    """Test that our modules can be imported."""
    print("\nTesting module imports...")
    
    # Add src to path
    sys.path.insert(0, str(Path(__file__).parent / "src"))
    
    modules = [
        'config',
        'jwt_generator',
        'oauth_client',
        'purestorage_client',
        'reporters',
        'main'
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"  ✓ {module}")
        except ImportError as e:
            print(f"  ✗ {module}: {e}")
            return False
    
    print("All modules imported successfully!")
    return True

def test_jwt_generation():
    """Test JWT token generation."""
    print("\nTesting JWT generation...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from jwt_generator import JWTGenerator
        
        # Test with the provided keys
        jwt_gen = JWTGenerator(
            private_key_path="private.pem",
            app_id="pure1:apikey:cajETP0O7YI55zQx"
        )
        
        token = jwt_gen.generate_token(expiry_days=1)
        print(f"  ✓ JWT token generated: {token[:50]}...")
        
        # Test token info
        info = jwt_gen.get_token_info(token)
        print(f"  ✓ Token expires at: {info.get('expires_at')}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ JWT generation failed: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from config import create_sample_config, load_config
        
        # Create sample config
        config_file = "test_config.yaml"
        create_sample_config(config_file)
        print(f"  ✓ Sample config created: {config_file}")
        
        # Load config
        config = load_config(config_file)
        print(f"  ✓ Configuration loaded successfully")
        print(f"    App ID: {config.purestorage.app_id}")
        print(f"    Collection interval: {config.monitoring.collection_interval}s")
        
        # Clean up
        os.remove(config_file)
        
        return True
        
    except Exception as e:
        print(f"  ✗ Configuration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("PureStorage Health Monitor Setup Test")
    print("=" * 40)
    
    tests = [
        test_dependencies,
        test_ssl_keys,
        test_imports,
        test_jwt_generation,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ Setup is complete and ready to use!")
        print("\nNext steps:")
        print("1. Edit config.yaml with your specific settings")
        print("2. Run: python -m src.main --test-auth")
        print("3. Run: python -m src.main --run-once")
        print("4. Deploy with: docker-compose up -d")
        return True
    else:
        print("✗ Setup has issues that need to be resolved.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
