# Bitbucket Pipeline Setup for PureStorage Health Monitor

This document provides instructions for setting up the Bitbucket Pipeline for automated building, testing, and deployment of the PureStorage Health Monitor.

## Overview

The pipeline automates the following workflow:
1. **Build & Test** - Validates Python code and dependencies
2. **Docker Build** - Creates container images
3. **Push to Quay** - Uploads images to Quay.io registry
4. **Deploy to Nomad** - Deploys to HashiCorp Nomad cluster

## Prerequisites

### 1. Quay.io Registry Setup

1. Create a Quay.io account and organization
2. Create a repository: `obs-purestorage`
3. Generate a robot account with push/pull permissions

### 2. Bitbucket Repository Variables

Configure the following repository variables in Bitbucket:

#### Required Variables
| Variable | Description | Example |
|----------|-------------|---------|
| `QUAY_USERNAME` | Quay.io username/robot account | `myorg+robot` |
| `QUAY_PASSWORD` | Quay.io password/token | `ABC123...` |
| `QUAY_NAMESPACE` | Quay.io organization/namespace | `myorganization` |
| `NOMAD_ADDR` | Nomad cluster address | `https://nomad.example.com:4646` |
| `CONSUL_HTTP_ADDR` | Consul address | `https://consul.example.com:8500` |

#### Optional Variables
| Variable | Description | Default |
|----------|-------------|---------|
| `ENVIRONMENT` | Deployment environment | `staging` |

### 3. Nomad/Consul Setup

Ensure your Nomad cluster has:
- Docker driver enabled
- Access to pull from Quay.io
- Consul KV configured with required keys (see below)

## Consul KV Configuration

The application requires the following Consul KV pairs to be configured:

```bash
# PureStorage API configuration
consul kv put purestorage/app_id "pure1:apikey:your-app-id"
consul kv put purestorage/private_key_passphrase "your-passphrase"

# OTLP configuration  
consul kv put purestorage/otlp_endpoint "https://your-otlp-endpoint:4317"
consul kv put purestorage/otlp_username "your-username"
consul kv put purestorage/otlp_password "your-password"

# SSL certificates
consul kv put purestorage/ssl/private_key @private.pem
consul kv put purestorage/ssl/public_key @public.pem
```

You can use the provided script to set these up:
```bash
./setup-consul-kv.sh
```

## Pipeline Workflows

### Default Branch (main)
- ✅ Build and test
- 🐳 Build Docker image
- 📤 Push to Quay registry
- 🚀 Deploy to Nomad

### Development Branch (develop)
- ✅ Build and test
- 🐳 Build Docker image
- 📤 Push to Quay registry

### Pull Requests
- ✅ Build and test
- 🐳 Build Docker image (not pushed)

### Tags (v*)
- ✅ Build and test
- 🐳 Build Docker image with version tag
- 📤 Push to Quay registry
- 🚀 Deploy to production

### Custom Pipelines
- `deploy-staging` - Deploy current code to staging
- `deploy-production` - Deploy current code to production

## Image Tagging Strategy

| Branch/Tag | Image Tag | Registry |
|------------|-----------|----------|
| `main` | `{commit-hash}`, `latest` | Quay.io |
| `develop` | `{commit-hash}` | Quay.io |
| `v1.2.3` | `v1.2.3`, `latest` | Quay.io |
| PR | `{commit-hash}` | Local only |

## Deployment Environments

### Staging
- **Trigger**: Push to `main` or `develop` branches
- **Job Name**: `purestorage-monitor-staging`
- **Image**: Latest commit hash
- **Consul KV Prefix**: `purestorage/`

### Production
- **Trigger**: Git tags matching `v*`
- **Job Name**: `purestorage-monitor`
- **Image**: Version tag
- **Consul KV Prefix**: `purestorage/`

## Manual Deployment

You can also deploy manually using the provided scripts:

```bash
# Deploy with specific image
export DOCKER_IMAGE=quay.io/myorg/obs-purestorage:v1.2.3
export ENVIRONMENT=production
./deploy-pipeline.sh

# Deploy to staging
export ENVIRONMENT=staging
./deploy-pipeline.sh
```

## Monitoring Deployments

### Bitbucket Pipeline Logs
Monitor the pipeline execution in Bitbucket:
1. Go to your repository
2. Click "Pipelines" in the left sidebar
3. View logs for each step

### Nomad Deployment Status
Check deployment status in Nomad:
```bash
# Check job status
nomad job status purestorage-monitor

# View allocations
nomad job allocs purestorage-monitor

# Follow logs
nomad alloc logs -f $(nomad job allocs -json purestorage-monitor | jq -r '.[0].ID')
```

### Application Health
The application includes health checks:
- **Authentication Test**: Runs every 5 minutes
- **Service Registration**: Registers with Consul
- **Metrics**: Available via OTLP endpoint

## Troubleshooting

### Pipeline Failures

#### Build/Test Step
- Check Python syntax errors
- Verify all dependencies in `requirements.txt`
- Review test output in pipeline logs

#### Docker Build Step
- Verify Dockerfile syntax
- Check if all required files are present
- Review Docker build logs

#### Push to Quay Step
- Verify Quay credentials in repository variables
- Check network connectivity to Quay.io
- Ensure repository exists and has correct permissions

#### Deploy to Nomad Step
- Verify Nomad cluster connectivity
- Check Consul KV configuration
- Review Nomad job validation errors
- Ensure Docker image is accessible from Nomad nodes

### Common Issues

1. **Authentication Failures**
   ```bash
   # Check Consul KV values
   consul kv get purestorage/app_id
   consul kv get purestorage/ssl/private_key
   ```

2. **Image Pull Failures**
   ```bash
   # Test image pull on Nomad node
   docker pull quay.io/myorg/obs-purestorage:latest
   ```

3. **Resource Constraints**
   ```bash
   # Check Nomad node resources
   nomad node status
   nomad alloc status <allocation-id>
   ```

## Security Considerations

- **Secrets Management**: All sensitive data stored in Bitbucket repository variables
- **Image Security**: Images scanned by Quay.io security scanner
- **Network Security**: Nomad jobs run in isolated network namespaces
- **Access Control**: Use Nomad ACLs and Consul ACLs in production

## Best Practices

1. **Version Control**: Always tag releases with semantic versioning
2. **Testing**: Run comprehensive tests before deployment
3. **Monitoring**: Set up alerts for deployment failures
4. **Rollback**: Keep previous versions available for quick rollback
5. **Documentation**: Update this document when making changes

## Support

For issues with the pipeline:
1. Check Bitbucket pipeline logs
2. Review Nomad job status
3. Verify Consul KV configuration
4. Check application logs in Nomad
