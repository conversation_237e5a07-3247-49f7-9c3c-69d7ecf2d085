"""
PureStorage API Client for Health Data Collection

This module provides a client for querying health data from the PureStorage Pure1 API,
including arrays, alerts, metrics, and hardware status information.
"""

import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
import logging
import time

logger = logging.getLogger(__name__)


class PureStorageClient:
    """Client for interacting with the PureStorage Pure1 API."""
    
    def __init__(self, oauth_client, base_url: str = "https://api.pure1.purestorage.com"):
        """
        Initialize the PureStorage client.
        
        Args:
            oauth_client: OAuth client instance for authentication
            base_url: Base URL for the PureStorage API
        """
        self.oauth_client = oauth_client
        self.base_url = base_url.rstrip('/')
        self.api_version = "1.latest"
        self.api_base = f"{self.base_url}/api/{self.api_version}"
    
    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make an authenticated request to the API.
        
        Args:
            endpoint: API endpoint (without base URL)
            params: Query parameters
            
        Returns:
            JSON response data
            
        Raises:
            requests.RequestException: If the request fails
        """
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        try:
            response = self.oauth_client.make_authenticated_request(
                'GET', url, params=params
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                error_msg = f"API request failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise requests.RequestException(error_msg)
                
        except requests.RequestException as e:
            logger.error(f"Request to {endpoint} failed: {e}")
            raise
    
    def get_arrays(self, limit: int = 100, **filters) -> List[Dict[str, Any]]:
        """
        Get information about FlashArray and FlashBlade storage appliances.
        
        Args:
            limit: Maximum number of results to return
            **filters: Additional filter parameters
            
        Returns:
            List of array information dictionaries
        """
        params = {'limit': limit}
        params.update(filters)
        
        logger.info(f"Fetching arrays with limit {limit}")
        response = self._make_request('arrays', params)
        
        arrays = response.get('items', [])
        logger.info(f"Retrieved {len(arrays)} arrays")
        return arrays

    def get_array_capacity_metrics(self, array_ids: List[str]) -> Dict[str, Dict[str, float]]:
        """
        Get capacity metrics for arrays.

        Args:
            array_ids: List of array IDs to get metrics for

        Returns:
            Dictionary mapping array_id to capacity metrics
        """
        if not array_ids:
            return {}

        # Get recent capacity metrics (last 24 hours)
        from datetime import datetime, timedelta
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)

        metric_names = [
            'array_total_capacity',  # Total physical usable space
            'array_unique_space'     # Physical space occupied by data (used space)
        ]

        try:
            logger.info(f"Fetching capacity metrics for {len(array_ids)} arrays")
            metrics_data = self.get_metrics_history(
                resource_ids=array_ids,
                metric_names=metric_names,
                start_time=start_time,
                end_time=end_time,
                resolution=86400000  # 1 day resolution
            )

            # Process metrics data into a more usable format
            capacity_by_array = {}

            for metric in metrics_data:
                array_id = metric.get('id')
                metric_name = metric.get('name')
                data_points = metric.get('data', [])

                if array_id not in capacity_by_array:
                    capacity_by_array[array_id] = {}

                # Get the most recent data point
                if data_points:
                    latest_value = data_points[-1][1]  # [timestamp, value]
                    if metric_name == 'array_total_capacity':
                        capacity_by_array[array_id]['total_capacity'] = latest_value
                    elif metric_name == 'array_unique_space':
                        capacity_by_array[array_id]['used_capacity'] = latest_value

            logger.info(f"Retrieved capacity metrics for {len(capacity_by_array)} arrays")
            return capacity_by_array

        except Exception as e:
            logger.warning(f"Failed to get array capacity metrics: {e}")
            return {}
    
    def get_alerts(self, limit: int = 100, severity: Optional[str] = None, 
                   state: Optional[str] = None, **filters) -> List[Dict[str, Any]]:
        """
        Get alerts from arrays.
        
        Args:
            limit: Maximum number of results to return
            severity: Filter by severity (info, warning, critical, hidden)
            state: Filter by state (open, closing, closed)
            **filters: Additional filter parameters
            
        Returns:
            List of alert dictionaries
        """
        params = {'limit': limit}
        
        if severity:
            params['filter'] = f"severity='{severity}'"
        if state:
            filter_param = params.get('filter', '')
            if filter_param:
                filter_param += f" and state='{state}'"
            else:
                filter_param = f"state='{state}'"
            params['filter'] = filter_param
        
        params.update(filters)
        
        logger.info(f"Fetching alerts with limit {limit}, severity={severity}, state={state}")
        response = self._make_request('alerts', params)
        
        alerts = response.get('items', [])
        logger.info(f"Retrieved {len(alerts)} alerts")
        return alerts
    
    def get_controllers(self, limit: int = 100, **filters) -> List[Dict[str, Any]]:
        """
        Get information about controllers.
        
        Args:
            limit: Maximum number of results to return
            **filters: Additional filter parameters
            
        Returns:
            List of controller information dictionaries
        """
        params = {'limit': limit}
        params.update(filters)
        
        logger.info(f"Fetching controllers with limit {limit}")
        response = self._make_request('controllers', params)
        
        controllers = response.get('items', [])
        logger.info(f"Retrieved {len(controllers)} controllers")
        return controllers
    
    def get_drives(self, limit: int = 100, **filters) -> List[Dict[str, Any]]:
        """
        Get information about drives.
        
        Args:
            limit: Maximum number of results to return
            **filters: Additional filter parameters
            
        Returns:
            List of drive information dictionaries
        """
        params = {'limit': limit}
        params.update(filters)
        
        logger.info(f"Fetching drives with limit {limit}")
        response = self._make_request('drives', params)
        
        drives = response.get('items', [])
        logger.info(f"Retrieved {len(drives)} drives")
        return drives
    
    def get_hardware(self, limit: int = 100, **filters) -> List[Dict[str, Any]]:
        """
        Get information about hardware components.
        
        Args:
            limit: Maximum number of results to return
            **filters: Additional filter parameters
            
        Returns:
            List of hardware information dictionaries
        """
        params = {'limit': limit}
        params.update(filters)
        
        logger.info(f"Fetching hardware with limit {limit}")
        response = self._make_request('hardware', params)
        
        hardware = response.get('items', [])
        logger.info(f"Retrieved {len(hardware)} hardware components")
        return hardware
    
    def get_metrics_history(self, resource_ids: List[str], metric_names: List[str],
                          start_time: datetime, end_time: datetime,
                          resolution: int = 300000, aggregation: str = 'avg') -> List[Dict[str, Any]]:
        """
        Get historical metric data for resources.
        
        Args:
            resource_ids: List of resource IDs to get metrics for
            metric_names: List of metric names to retrieve
            start_time: Start time for metrics
            end_time: End time for metrics
            resolution: Resolution in milliseconds (default: 5 minutes)
            aggregation: Aggregation method (avg, max)
            
        Returns:
            List of metric data dictionaries
        """
        params = {
            'resource_ids': ','.join(f"'{rid}'" for rid in resource_ids),
            'names': ','.join(f"'{name}'" for name in metric_names),
            'start_time': int(start_time.timestamp() * 1000),
            'end_time': int(end_time.timestamp() * 1000),
            'resolution': resolution,
            'aggregation': f"'{aggregation}'"
        }
        
        logger.info(f"Fetching metrics for {len(resource_ids)} resources, {len(metric_names)} metrics")
        response = self._make_request('metrics/history', params)
        
        metrics = response.get('items', [])
        logger.info(f"Retrieved {len(metrics)} metric series")
        return metrics
    
    def get_available_metrics(self, resource_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get available metrics for resource types.
        
        Args:
            resource_types: List of resource types to get metrics for
            
        Returns:
            List of available metric definitions
        """
        params = {}
        if resource_types:
            params['resource_types'] = ','.join(f"'{rt}'" for rt in resource_types)
        
        logger.info(f"Fetching available metrics for resource types: {resource_types}")
        response = self._make_request('metrics', params)
        
        metrics = response.get('items', [])
        logger.info(f"Retrieved {len(metrics)} available metrics")
        return metrics
    
    def get_volumes(self, limit: int = 100, **filters) -> List[Dict[str, Any]]:
        """
        Get information about volumes.
        
        Args:
            limit: Maximum number of results to return
            **filters: Additional filter parameters
            
        Returns:
            List of volume information dictionaries
        """
        params = {'limit': limit}
        params.update(filters)
        
        logger.info(f"Fetching volumes with limit {limit}")
        response = self._make_request('volumes', params)
        
        volumes = response.get('items', [])
        logger.info(f"Retrieved {len(volumes)} volumes")
        return volumes
    
    def get_health_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive health summary of all monitored resources.
        
        Returns:
            Dictionary containing health summary data
        """
        logger.info("Collecting comprehensive health summary")
        
        summary = {
            'timestamp': datetime.utcnow().isoformat(),
            'arrays': [],
            'alerts': [],
            'controllers': [],
            'drives': [],
            'hardware': [],
            'volumes': []
        }
        
        try:
            # Get arrays
            arrays = self.get_arrays(limit=50)
            summary['arrays'] = arrays

            # Get array capacity metrics
            if arrays:
                array_ids = [array['id'] for array in arrays]
                capacity_metrics = self.get_array_capacity_metrics(array_ids)

                # Add capacity data to array objects
                for array in summary['arrays']:
                    array_id = array['id']
                    if array_id in capacity_metrics:
                        array['space'] = capacity_metrics[array_id]
            
            # Get critical and warning alerts
            critical_alerts = self.get_alerts(limit=100, severity='critical', state='open')
            warning_alerts = self.get_alerts(limit=100, severity='warning', state='open')
            summary['alerts'] = critical_alerts + warning_alerts
            
            # Get controllers
            summary['controllers'] = self.get_controllers(limit=100)
            
            # Get drives
            summary['drives'] = self.get_drives(limit=200)
            
            # Get hardware
            summary['hardware'] = self.get_hardware(limit=200)
            
            # Get volumes (sample)
            summary['volumes'] = self.get_volumes(limit=50)
            
            logger.info(f"Health summary collected: {len(summary['arrays'])} arrays, "
                       f"{len(summary['alerts'])} alerts, {len(summary['controllers'])} controllers")
            
        except Exception as e:
            logger.error(f"Error collecting health summary: {e}")
            summary['error'] = str(e)
        
        return summary


def main():
    """Test the PureStorage client."""
    import sys
    import os
    
    # Add src to path for imports
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    
    from src.jwt_generator import JWTGenerator
    from src.oauth_client import OAuthClient
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Test configuration
    private_key_path = "private.pem"
    app_id = "pure1:apikey:cajETP0O7YI55zQx"
    
    try:
        # Create clients
        jwt_gen = JWTGenerator(private_key_path, app_id)
        oauth_client = OAuthClient()
        
        # Authenticate
        jwt_token = jwt_gen.generate_token()
        oauth_client.exchange_token(jwt_token)
        
        # Create PureStorage client
        ps_client = PureStorageClient(oauth_client)
        
        # Test API calls
        print("Testing PureStorage API client...")
        
        # Get arrays
        arrays = ps_client.get_arrays(limit=5)
        print(f"Found {len(arrays)} arrays")
        
        # Get alerts
        alerts = ps_client.get_alerts(limit=10, severity='critical')
        print(f"Found {len(alerts)} critical alerts")
        
        # Get available metrics
        metrics = ps_client.get_available_metrics(['arrays'])
        print(f"Found {len(metrics)} available metrics for arrays")
        
        # Get health summary
        health = ps_client.get_health_summary()
        print(f"Health summary collected at {health['timestamp']}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
