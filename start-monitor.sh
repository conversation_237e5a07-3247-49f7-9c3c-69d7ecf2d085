#!/bin/bash

# PureStorage Health Monitor - Production Startup Script

set -e

echo "🚀 Starting PureStorage Health Monitor in production mode..."

# Check if config file exists
if [ ! -f "config.yaml" ]; then
    echo "❌ Error: config.yaml not found!"
    echo "Please ensure your configuration file is present."
    exit 1
fi

# Check if private key exists
if [ ! -f "private.pem" ]; then
    echo "❌ Error: private.pem not found!"
    echo "Please ensure your private key file is present."
    exit 1
fi

# Build the latest image
echo "🔨 Building Docker image..."
docker-compose build

# Test authentication first
echo "🔐 Testing authentication..."
if ! docker-compose run --rm purestorage-monitor python -m src.main --test-auth; then
    echo "❌ Authentication test failed!"
    echo "Please check your configuration and try again."
    exit 1
fi

echo "✅ Authentication test passed!"

# Stop any existing containers
echo "🛑 Stopping any existing containers..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# Start the monitor in production mode
echo "🚀 Starting monitor in continuous mode..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Show status
echo "📊 Container status:"
docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps

echo ""
echo "✅ PureStorage Health Monitor started successfully!"
echo ""
echo "📋 Useful commands:"
echo "  View logs:    docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f"
echo "  Stop monitor: docker-compose -f docker-compose.yml -f docker-compose.prod.yml down"
echo "  Restart:      docker-compose -f docker-compose.yml -f docker-compose.prod.yml restart"
echo "  Status:       docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps"
echo ""
