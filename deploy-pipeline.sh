#!/bin/bash

# PureStorage Health Monitor - Pipeline Deployment Script
# This script is designed to be used in Bitbucket Pipelines for automated deployment

set -e

# Default values
ENVIRONMENT=${ENVIRONMENT:-"staging"}
NOMAD_JOB_FILE=${NOMAD_JOB_FILE:-"purestorage-monitor.nomad"}
DOCKER_IMAGE=${DOCKER_IMAGE:-"quay.io/your-namespace/obs-purestorage:latest"}

echo "🚀 PureStorage Health Monitor - Pipeline Deployment"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Docker Image: $DOCKER_IMAGE"
echo "Nomad Job File: $NOMAD_JOB_FILE"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for deployment
wait_for_deployment() {
    local job_name="purestorage-monitor"
    local max_wait=300  # 5 minutes
    local wait_time=0
    
    echo "⏳ Waiting for deployment to be healthy..."
    
    while [ $wait_time -lt $max_wait ]; do
        if nomad job status "$job_name" | grep -q "Status.*running"; then
            echo "✅ Deployment is running!"
            return 0
        fi
        
        echo "⏳ Still waiting... ($wait_time/$max_wait seconds)"
        sleep 10
        wait_time=$((wait_time + 10))
    done
    
    echo "❌ Deployment did not become healthy within $max_wait seconds"
    nomad job status "$job_name"
    return 1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists nomad; then
    echo "❌ Error: Nomad CLI not found!"
    exit 1
fi

if ! command_exists consul; then
    echo "⚠️  Warning: Consul CLI not found. Some operations may not work."
fi

# Verify Nomad connection
if ! nomad node status >/dev/null 2>&1; then
    echo "❌ Error: Cannot connect to Nomad cluster!"
    echo "Please check NOMAD_ADDR environment variable: $NOMAD_ADDR"
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Check if Nomad job file exists
if [ ! -f "$NOMAD_JOB_FILE" ]; then
    echo "❌ Error: $NOMAD_JOB_FILE not found!"
    exit 1
fi

# Create a temporary job file with the correct image
TEMP_JOB_FILE=$(mktemp)
cp "$NOMAD_JOB_FILE" "$TEMP_JOB_FILE"

# Update the Docker image in the job file
echo "🔧 Updating Docker image to: $DOCKER_IMAGE"
sed -i "s|image = \"purestorage-monitor:latest\"|image = \"$DOCKER_IMAGE\"|g" "$TEMP_JOB_FILE"
sed -i "s|image = \".*obs-purestorage.*\"|image = \"$DOCKER_IMAGE\"|g" "$TEMP_JOB_FILE"

# Update job name for different environments
if [ "$ENVIRONMENT" != "production" ]; then
    echo "🔧 Updating job name for environment: $ENVIRONMENT"
    sed -i "s|job \"purestorage-monitor\"|job \"purestorage-monitor-$ENVIRONMENT\"|g" "$TEMP_JOB_FILE"
    sed -i "s|name = \"purestorage-monitor\"|name = \"purestorage-monitor-$ENVIRONMENT\"|g" "$TEMP_JOB_FILE"
fi

# Validate Nomad job
echo ""
echo "🔍 Validating Nomad job specification..."
if nomad job validate "$TEMP_JOB_FILE"; then
    echo "✅ Nomad job validation passed!"
else
    echo "❌ Nomad job validation failed!"
    cat "$TEMP_JOB_FILE"
    rm -f "$TEMP_JOB_FILE"
    exit 1
fi

# Check if this is an update or new deployment
JOB_NAME="purestorage-monitor"
if [ "$ENVIRONMENT" != "production" ]; then
    JOB_NAME="purestorage-monitor-$ENVIRONMENT"
fi

if nomad job status "$JOB_NAME" >/dev/null 2>&1; then
    echo "📦 Updating existing job: $JOB_NAME"
    DEPLOYMENT_TYPE="update"
else
    echo "🆕 Creating new job: $JOB_NAME"
    DEPLOYMENT_TYPE="new"
fi

# Deploy the job
echo ""
echo "🚀 Deploying to Nomad..."
if nomad job run "$TEMP_JOB_FILE"; then
    echo "✅ Job deployed successfully!"
else
    echo "❌ Job deployment failed!"
    rm -f "$TEMP_JOB_FILE"
    exit 1
fi

# Clean up temporary file
rm -f "$TEMP_JOB_FILE"

# Wait for deployment to be healthy
wait_for_deployment

# Show job status
echo ""
echo "📊 Final Job Status:"
nomad job status "$JOB_NAME"

# Show allocations
echo ""
echo "📋 Allocations:"
nomad job allocs "$JOB_NAME"

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Useful commands:"
echo "  View job status:    nomad job status $JOB_NAME"
echo "  View allocations:   nomad job allocs $JOB_NAME"
echo "  View logs:          nomad alloc logs -f \$(nomad job allocs -json $JOB_NAME | jq -r '.[0].ID')"
echo "  Stop job:           nomad job stop $JOB_NAME"
echo "  Restart job:        nomad job restart $JOB_NAME"
echo ""

# If this is a production deployment, show additional info
if [ "$ENVIRONMENT" = "production" ]; then
    echo "🎯 Production Deployment Notes:"
    echo "  - Monitor the application logs for any issues"
    echo "  - Check metrics in your observability platform"
    echo "  - Verify health checks are passing"
    echo "  - Consider running a smoke test"
    echo ""
fi
