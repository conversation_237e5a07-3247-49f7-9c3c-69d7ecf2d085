version: '3.8'

services:
  purestorage-monitor:
    # Override the command to run continuously
    command: python -m src.main --continuous
    
    # Restart policy for production
    restart: unless-stopped
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Environment variables for production
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Australia/Sydney
