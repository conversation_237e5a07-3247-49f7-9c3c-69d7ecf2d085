# Bitbucket Pipeline Setup - Complete Summary

## 🎯 What Was Created

The following files have been created to set up a complete CI/CD pipeline for the PureStorage Health Monitor:

### Core Pipeline Files
- **`bitbucket-pipelines.yml`** - Main pipeline configuration
- **`deploy-pipeline.sh`** - Deployment script for Nomad
- **`validate-pipeline.sh`** - Pipeline validation script
- **`test-build.sh`** - Local Docker build testing script

### Documentation
- **`BITBUCKET-PIPELINE.md`** - Comprehensive pipeline setup guide
- **`PIPELINE-SETUP-SUMMARY.md`** - This summary document

### Updated Files
- **`purestorage-monitor.nomad`** - Updated to use Quay registry image

## 🚀 Pipeline Workflow

### Branches and Triggers

| Branch/Event | Actions | Deployment |
|--------------|---------|------------|
| **main** | Build → Test → Push → Deploy | Staging |
| **develop** | Build → Test → Push | None |
| **Pull Requests** | Build → Test | None |
| **Tags (v\*)** | Build → Test → Push → Deploy | Production |

### Image Strategy

| Source | Image Tag | Registry | Notes |
|--------|-----------|----------|-------|
| main branch | `{commit-hash}`, `latest` | Quay.io | Auto-deploy to staging |
| develop branch | `{commit-hash}` | Quay.io | No deployment |
| version tags | `{version}`, `latest` | Quay.io | Auto-deploy to production |
| pull requests | `{commit-hash}` | Local only | Build validation only |

## ⚙️ Required Configuration

### 1. Bitbucket Repository Variables

Set these in your Bitbucket repository settings:

```bash
# Required
QUAY_USERNAME=myorg+robot
QUAY_PASSWORD=ABC123...
QUAY_NAMESPACE=myorganization
NOMAD_ADDR=https://nomad.example.com:4646

# Optional
CONSUL_HTTP_ADDR=https://consul.example.com:8500
ENVIRONMENT=staging
```

### 2. Quay.io Setup

1. Create account and organization
2. Create repository: `obs-purestorage`
3. Generate robot account with push/pull permissions
4. Use robot credentials in Bitbucket variables

### 3. Consul KV Configuration

Run the setup script to configure required keys:

```bash
./setup-consul-kv.sh
```

Or set manually:
```bash
consul kv put purestorage/app_id "pure1:apikey:your-app-id"
consul kv put purestorage/private_key_passphrase "your-passphrase"
consul kv put purestorage/otlp_endpoint "https://your-otlp-endpoint:4317"
consul kv put purestorage/otlp_username "your-username"
consul kv put purestorage/otlp_password "your-password"
consul kv put purestorage/ssl/private_key @private.pem
consul kv put purestorage/ssl/public_key @public.pem
```

## 🧪 Testing Before Deployment

### 1. Validate Pipeline Configuration
```bash
./validate-pipeline.sh
```

### 2. Test Docker Build Locally
```bash
./test-build.sh
```

### 3. Test Nomad Deployment
```bash
# Set environment variables
export DOCKER_IMAGE=quay.io/myorg/obs-purestorage:test
export ENVIRONMENT=staging

# Run deployment
./deploy-pipeline.sh
```

## 📋 Deployment Process

### Automatic Deployment

1. **Push to main** → Deploys to staging
2. **Create tag** → Deploys to production

### Manual Deployment

Use custom pipelines in Bitbucket:
- `deploy-staging` - Deploy current code to staging
- `deploy-production` - Deploy current code to production

### Environment Differences

| Environment | Job Name | Consul KV Prefix | Image Source |
|-------------|----------|------------------|--------------|
| Staging | `purestorage-monitor-staging` | `purestorage/` | Latest commit |
| Production | `purestorage-monitor` | `purestorage/` | Version tag |

## 🔍 Monitoring and Troubleshooting

### Pipeline Monitoring
- View pipeline status in Bitbucket
- Check build logs for each step
- Monitor deployment status in Nomad

### Common Issues

1. **Build Failures**
   - Check Python syntax
   - Verify dependencies
   - Review Docker build logs

2. **Push Failures**
   - Verify Quay credentials
   - Check repository permissions
   - Ensure network connectivity

3. **Deployment Failures**
   - Verify Nomad connectivity
   - Check Consul KV configuration
   - Review job validation errors

### Useful Commands

```bash
# Check Nomad deployment
nomad job status purestorage-monitor
nomad job allocs purestorage-monitor
nomad alloc logs -f <allocation-id>

# Check Consul configuration
consul kv get -keys purestorage/

# Test Docker image
docker run --rm quay.io/myorg/obs-purestorage:latest python -m src.main --test-auth
```

## 🔒 Security Considerations

- All secrets stored in Bitbucket repository variables
- SSL keys managed through Consul KV
- Images scanned by Quay.io security scanner
- Nomad jobs run with minimal privileges

## 📚 Additional Resources

- **[BITBUCKET-PIPELINE.md](BITBUCKET-PIPELINE.md)** - Detailed pipeline documentation
- **[NOMAD-DEPLOYMENT.md](NOMAD-DEPLOYMENT.md)** - Nomad deployment guide
- **[README.md](README.md)** - Application documentation

## ✅ Next Steps

1. **Configure Bitbucket Variables** - Set up required repository variables
2. **Set up Quay.io** - Create registry and robot account
3. **Configure Consul KV** - Run `./setup-consul-kv.sh`
4. **Validate Setup** - Run `./validate-pipeline.sh`
5. **Test Build** - Run `./test-build.sh`
6. **Commit and Push** - Trigger your first pipeline run
7. **Monitor Deployment** - Check Nomad for successful deployment

## 🎉 Success Criteria

Your pipeline is working correctly when:
- ✅ Code pushes trigger automatic builds
- ✅ Images are pushed to Quay registry
- ✅ Deployments complete successfully in Nomad
- ✅ Application health checks pass
- ✅ Metrics are flowing to your OTLP endpoint

---

**Need Help?** Check the troubleshooting sections in the documentation or review the pipeline logs in Bitbucket.
