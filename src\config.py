"""
Configuration Management for PureStorage Health Monitor

This module handles configuration loading from files and environment variables.
"""

import os
from pathlib import Path
from typing import Optional, List, Dict
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings
import logging

logger = logging.getLogger(__name__)


class PureStorageConfig(BaseModel):
    """PureStorage API configuration."""

    app_id: str = Field(..., description="PureStorage API application ID")
    private_key_path: str = Field(..., description="Path to private key file")
    public_key_path: Optional[str] = Field(None, description="Path to public key file")
    private_key_passphrase: Optional[str] = Field(None, description="Private key passphrase")
    api_base_url: str = Field("https://api.pure1.purestorage.com", description="PureStorage API base URL")
    token_expiry_days: int = Field(30, description="JWT token expiry in days")
    proxy_url: Optional[str] = Field(None, description="HTTP/HTTPS proxy URL (e.g., http://proxy.example.com:8080)")
    
    @validator('private_key_path')
    def validate_private_key_path(cls, v):
        if not Path(v).exists():
            raise ValueError(f"Private key file not found: {v}")
        return v
    
    @validator('public_key_path')
    def validate_public_key_path(cls, v):
        if v and not Path(v).exists():
            raise ValueError(f"Public key file not found: {v}")
        return v


class OTLPConfig(BaseModel):
    """OTLP configuration."""

    enabled: bool = Field(True, description="Enable OTLP reporting")
    endpoint: str = Field(..., description="OTLP endpoint URL")
    headers: Optional[Dict[str, str]] = Field(default_factory=dict, description="OTLP headers for authentication")
    username: Optional[str] = Field(None, description="Username for basic authentication")
    password: Optional[str] = Field(None, description="Password for basic authentication")
    timeout: int = Field(30, description="Request timeout in seconds")
    service_name: str = Field("purestorage-health-monitor", description="Service name for telemetry")

    @validator('endpoint')
    def validate_endpoint(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("OTLP endpoint must start with http:// or https://")
        return v


class LogConfig(BaseModel):
    """Log-based reporting configuration."""

    enabled: bool = Field(False, description="Enable log-based reporting")
    level: str = Field("INFO", description="Log level for health reports")

    @validator('level')
    def validate_level(cls, v):
        valid_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}")
        return v.upper()


class MonitoringConfig(BaseModel):
    """Monitoring and collection configuration."""
    
    collection_interval: int = Field(300, description="Data collection interval in seconds")
    max_alerts: int = Field(1000, description="Maximum number of alerts to fetch")
    max_arrays: int = Field(100, description="Maximum number of arrays to fetch")
    max_hardware: int = Field(500, description="Maximum number of hardware components to fetch")
    max_drives: int = Field(1000, description="Maximum number of drives to fetch")
    max_controllers: int = Field(200, description="Maximum number of controllers to fetch")
    max_volumes: int = Field(500, description="Maximum number of volumes to fetch")
    
    # Alert filtering
    alert_severities: List[str] = Field(
        ['critical', 'warning'], 
        description="Alert severities to collect"
    )
    alert_states: List[str] = Field(
        ['open'], 
        description="Alert states to collect"
    )
    
    # Metrics configuration
    metrics_enabled: bool = Field(True, description="Enable metrics collection")
    metrics_resolution: int = Field(300000, description="Metrics resolution in milliseconds")
    metrics_history_hours: int = Field(24, description="Hours of metrics history to collect")
    
    @validator('collection_interval')
    def validate_collection_interval(cls, v):
        if v < 60:
            raise ValueError("Collection interval must be at least 60 seconds")
        return v
    
    @validator('alert_severities')
    def validate_alert_severities(cls, v):
        valid_severities = {'info', 'warning', 'critical', 'hidden'}
        for severity in v:
            if severity not in valid_severities:
                raise ValueError(f"Invalid alert severity: {severity}")
        return v
    
    @validator('alert_states')
    def validate_alert_states(cls, v):
        valid_states = {'open', 'closing', 'closed'}
        for state in v:
            if state not in valid_states:
                raise ValueError(f"Invalid alert state: {state}")
        return v


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = Field("INFO", description="Log level")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format"
    )
    file: Optional[str] = Field(None, description="Log file path")
    max_size: int = Field(10485760, description="Max log file size in bytes (10MB)")
    backup_count: int = Field(5, description="Number of backup log files")
    
    @validator('level')
    def validate_level(cls, v):
        valid_levels = {'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'}
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}")
        return v.upper()


class Config(BaseSettings):
    """Main configuration class."""

    purestorage: PureStorageConfig
    otlp: Optional[OTLPConfig] = None
    log_reporting: Optional[LogConfig] = LogConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    logging: LoggingConfig = LoggingConfig()

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False


def load_config(config_file: Optional[str] = None) -> Config:
    """
    Load configuration from file and environment variables.
    
    Args:
        config_file: Optional path to configuration file
        
    Returns:
        Configuration object
    """
    config_data = {}
    
    # Load from file if provided
    if config_file and Path(config_file).exists():
        import yaml
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_file}")
    
    # Create config object (will also load from environment)
    try:
        config = Config(**config_data)
        logger.info("Configuration loaded successfully")
        return config
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise


def setup_logging(logging_config: LoggingConfig):
    """Set up logging based on configuration."""
    import logging.handlers
    
    # Create logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, logging_config.level))
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(logging_config.format)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler if specified
    if logging_config.file:
        file_handler = logging.handlers.RotatingFileHandler(
            logging_config.file,
            maxBytes=logging_config.max_size,
            backupCount=logging_config.backup_count
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    logger.info(f"Logging configured: level={logging_config.level}, file={logging_config.file}")


def create_sample_config(output_file: str = "config.yaml"):
    """Create a sample configuration file."""
    sample_config = {
        'purestorage': {
            'app_id': 'pure1:apikey:cajETP0O7YI55zQx',
            'private_key_path': 'private.pem',
            'public_key_path': 'public.pem',
            'private_key_passphrase': None,
            'api_base_url': 'https://api.pure1.purestorage.com',
            'token_expiry_days': 30
        },
        'otlp': {
            'enabled': True,
            'endpoint': 'http://localhost:4317',
            'headers': {},
            'timeout': 30,
            'service_name': 'purestorage-health-monitor'
        },
        'log_reporting': {
            'enabled': False,
            'level': 'INFO'
        },
        'monitoring': {
            'collection_interval': 300,
            'max_alerts': 1000,
            'max_arrays': 100,
            'max_hardware': 500,
            'max_drives': 1000,
            'max_controllers': 200,
            'max_volumes': 500,
            'alert_severities': ['critical', 'warning'],
            'alert_states': ['open'],
            'metrics_enabled': True,
            'metrics_resolution': 300000,
            'metrics_history_hours': 24
        },
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'purestorage_monitor.log',
            'max_size': 10485760,
            'backup_count': 5
        }
    }
    
    import yaml
    with open(output_file, 'w') as f:
        yaml.dump(sample_config, f, default_flow_style=False, indent=2)
    
    print(f"Sample configuration created: {output_file}")


def main():
    """Test configuration loading."""
    # Create sample config
    create_sample_config()
    
    # Test loading
    try:
        config = load_config("config.yaml")
        print("Configuration loaded successfully:")
        print(f"  PureStorage App ID: {config.purestorage.app_id}")
        print(f"  Collection interval: {config.monitoring.collection_interval}s")
        print(f"  OTLP enabled: {config.otlp.enabled if config.otlp else False}")
        print(f"  Log reporting enabled: {config.log_reporting.enabled if config.log_reporting else False}")
        
        # Test logging setup
        setup_logging(config.logging)
        logger.info("Configuration test completed successfully")
        
    except Exception as e:
        print(f"Configuration test failed: {e}")


if __name__ == "__main__":
    main()
