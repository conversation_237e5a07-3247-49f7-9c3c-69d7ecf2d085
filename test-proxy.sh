#!/bin/bash

# Test script to debug proxy connectivity issues

echo "🔍 Proxy Connectivity Test"
echo "========================="

PROXY_HOST="proxy.det.nsw.edu.au"
PROXY_PORT="80"
PROXY_URL="http://${PROXY_HOST}:${PROXY_PORT}"

echo ""
echo "Testing proxy: $PROXY_URL"

# Test 1: Basic connectivity
echo ""
echo "Test 1: Basic connectivity to proxy host"
if ping -c 1 "$PROXY_HOST" >/dev/null 2>&1; then
    echo "✅ Proxy host $PROXY_HOST is reachable"
else
    echo "❌ Proxy host $PROXY_HOST is NOT reachable"
    echo "   This could be a DNS issue or network connectivity problem"
fi

# Test 2: Port connectivity
echo ""
echo "Test 2: Port connectivity"
if command -v nc >/dev/null 2>&1; then
    if nc -z "$PROXY_HOST" "$PROXY_PORT" 2>/dev/null; then
        echo "✅ Proxy port $PROXY_PORT is open on $PROXY_HOST"
    else
        echo "❌ Proxy port $PROXY_PORT is NOT accessible on $PROXY_HOST"
    fi
else
    echo "⚠️  netcat (nc) not available, skipping port test"
fi

# Test 3: DNS resolution
echo ""
echo "Test 3: DNS resolution"
if nslookup "$PROXY_HOST" >/dev/null 2>&1; then
    echo "✅ DNS resolution for $PROXY_HOST works"
    echo "   IP: $(nslookup "$PROXY_HOST" | grep -A1 "Name:" | tail -1 | awk '{print $2}')"
else
    echo "❌ DNS resolution for $PROXY_HOST failed"
    echo "   This is likely the root cause of the proxy issues"
fi

# Test 4: HTTP connectivity through proxy
echo ""
echo "Test 4: HTTP connectivity through proxy"
if command -v curl >/dev/null 2>&1; then
    if curl -s --proxy "$PROXY_URL" --connect-timeout 10 http://httpbin.org/ip >/dev/null 2>&1; then
        echo "✅ HTTP requests through proxy work"
    else
        echo "❌ HTTP requests through proxy failed"
    fi
else
    echo "⚠️  curl not available, skipping HTTP test"
fi

# Test 5: Environment variables
echo ""
echo "Test 5: Current proxy environment variables"
if [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ] || [ -n "$http_proxy" ] || [ -n "$https_proxy" ]; then
    echo "Current proxy environment variables:"
    [ -n "$HTTP_PROXY" ] && echo "  HTTP_PROXY=$HTTP_PROXY"
    [ -n "$HTTPS_PROXY" ] && echo "  HTTPS_PROXY=$HTTPS_PROXY"
    [ -n "$http_proxy" ] && echo "  http_proxy=$http_proxy"
    [ -n "$https_proxy" ] && echo "  https_proxy=$https_proxy"
    [ -n "$NO_PROXY" ] && echo "  NO_PROXY=$NO_PROXY"
    [ -n "$no_proxy" ] && echo "  no_proxy=$no_proxy"
else
    echo "No proxy environment variables are set"
fi

# Test 6: APT proxy configuration
echo ""
echo "Test 6: APT proxy configuration"
if [ -f "/etc/apt/apt.conf.d/00proxy" ]; then
    echo "APT proxy configuration exists:"
    cat /etc/apt/apt.conf.d/00proxy
else
    echo "No APT proxy configuration found"
fi

echo ""
echo "🔧 Recommendations:"
echo "=================="

if ! ping -c 1 "$PROXY_HOST" >/dev/null 2>&1; then
    echo "❌ Primary Issue: Proxy host is not reachable"
    echo "   Solutions:"
    echo "   1. Check if you're running this from the correct network"
    echo "   2. Verify the proxy hostname is correct"
    echo "   3. Check if VPN connection is required"
    echo "   4. Try running without proxy if on a direct connection"
    echo ""
    echo "   For Bitbucket Pipeline:"
    echo "   - The pipeline will automatically detect this and skip proxy configuration"
    echo "   - This should allow the build to proceed without proxy"
else
    echo "✅ Proxy connectivity looks good"
    echo "   The pipeline should work with proxy configuration"
fi

echo ""
echo "🚀 Next Steps:"
echo "============="
echo "1. If proxy is not reachable, the pipeline will automatically skip proxy setup"
echo "2. The build should proceed using direct internet connection"
echo "3. If you're in a restricted environment, contact your network administrator"
echo ""
