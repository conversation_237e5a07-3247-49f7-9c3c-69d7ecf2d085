"""
Data Reporters for OTLP Endpoints

This module provides reporters to send PureStorage health data to
OTLP endpoints for metrics and logs.
"""

import json
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

from opentelemetry import metrics, trace
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
import base64
from opentelemetry.sdk.resources import Resource

logger = logging.getLogger(__name__)


class OTLPReporter:
    """Reporter for sending metrics and traces to OTLP endpoints."""

    def __init__(self, otlp_endpoint: str, headers: Optional[Dict[str, str]] = None,
                 service_name: str = "purestorage-health-monitor",
                 username: Optional[str] = None, password: Optional[str] = None,
                 proxy_url: Optional[str] = None):
        """
        Initialize the OTLP reporter.

        Args:
            otlp_endpoint: URL of the OTLP endpoint
            headers: Optional headers for authentication
            service_name: Service name for telemetry data
            username: Username for basic authentication
            password: Password for basic authentication
            proxy_url: HTTP/HTTPS proxy URL
        """
        self.otlp_endpoint = otlp_endpoint.rstrip('/')
        self.service_name = service_name
        self.proxy_url = proxy_url

        # Set up authentication headers
        if username and password:
            # Use basic authentication
            credentials = f"{username}:{password}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            self.headers = {"Authorization": f"Basic {encoded_credentials}"}
        else:
            # Use provided headers
            self.headers = headers or {}

        # Configure proxy environment variables if proxy is provided
        if self.proxy_url:
            import os
            os.environ['HTTP_PROXY'] = self.proxy_url
            os.environ['HTTPS_PROXY'] = self.proxy_url
            # Disable SSL verification for corporate proxy environments
            os.environ['OTEL_EXPORTER_OTLP_INSECURE'] = 'true'
            # Disable SSL warnings
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            logger.info(f"Configured OTLP proxy: {self.proxy_url} (SSL verification disabled)")

        # Set up resource
        self.resource = Resource.create({
            "service.name": service_name,
            "service.version": "1.0.0",
            "service.instance.id": f"{service_name}-{int(time.time())}"
        })

        self._setup_telemetry()
    
    def _setup_telemetry(self):
        """Set up OpenTelemetry metrics and tracing."""
        # Set up metrics with correct endpoint path
        metrics_endpoint = f"{self.otlp_endpoint}/v1/metrics"
        metric_exporter = OTLPMetricExporter(
            endpoint=metrics_endpoint,
            headers=self.headers
        )

        metric_reader = PeriodicExportingMetricReader(
            exporter=metric_exporter,
            export_interval_millis=30000  # Export every 30 seconds
        )

        meter_provider = MeterProvider(
            resource=self.resource,
            metric_readers=[metric_reader]
        )

        metrics.set_meter_provider(meter_provider)
        self.meter = metrics.get_meter(self.service_name)

        # Set up tracing with correct endpoint path
        traces_endpoint = f"{self.otlp_endpoint}/v1/traces"
        trace_exporter = OTLPSpanExporter(
            endpoint=traces_endpoint,
            headers=self.headers
        )

        trace_provider = TracerProvider(resource=self.resource)
        trace_provider.add_span_processor(BatchSpanProcessor(trace_exporter))
        trace.set_tracer_provider(trace_provider)
        self.tracer = trace.get_tracer(self.service_name)

        self._create_instruments()

    def _create_instruments(self):
        """Create OpenTelemetry instruments for metrics."""
        # Store current gauge values
        self._gauge_values = {}

        # Create observable gauges for status and capacity metrics
        # These will provide the current state values

        # Array health metrics
        self.array_status_gauge = self.meter.create_observable_gauge(
            name="purestorage_array_status",
            description="Array status (1=healthy, 0=unhealthy)",
            unit="1",
            callbacks=[self._get_array_status_values]
        )

        self.array_capacity_total_gauge = self.meter.create_observable_gauge(
            name="purestorage_array_capacity_total_bytes",
            description="Total array capacity in bytes",
            unit="bytes",
            callbacks=[self._get_array_capacity_total_values]
        )

        self.array_capacity_used_gauge = self.meter.create_observable_gauge(
            name="purestorage_array_capacity_used_bytes",
            description="Used array capacity in bytes",
            unit="bytes",
            callbacks=[self._get_array_capacity_used_values]
        )

        # Alert metrics (counter for total count)
        self.alert_count_counter = self.meter.create_counter(
            name="purestorage_alerts_total",
            description="Number of alerts by severity and state",
            unit="1"
        )

        # Hardware metrics
        self.hardware_status_gauge = self.meter.create_observable_gauge(
            name="purestorage_hardware_status",
            description="Hardware component status (1=healthy, 0=unhealthy)",
            unit="1",
            callbacks=[self._get_hardware_status_values]
        )

        # Drive metrics
        self.drive_status_gauge = self.meter.create_observable_gauge(
            name="purestorage_drive_status",
            description="Drive status (1=healthy, 0=unhealthy)",
            unit="1",
            callbacks=[self._get_drive_status_values]
        )

        # Controller metrics
        self.controller_status_gauge = self.meter.create_observable_gauge(
            name="purestorage_controller_status",
            description="Controller status (1=healthy, 0=unhealthy)",
            unit="1",
            callbacks=[self._get_controller_status_values]
        )

        # Volume metrics
        self.volume_status_gauge = self.meter.create_observable_gauge(
            name="purestorage_volume_status",
            description="Volume status (1=healthy, 0=unhealthy)",
            unit="1",
            callbacks=[self._get_volume_status_values]
        )

        self.volume_capacity_total_gauge = self.meter.create_observable_gauge(
            name="purestorage_volume_capacity_total_bytes",
            description="Total volume capacity in bytes",
            unit="bytes",
            callbacks=[self._get_volume_capacity_total_values]
        )

        self.volume_capacity_used_gauge = self.meter.create_observable_gauge(
            name="purestorage_volume_capacity_used_bytes",
            description="Used volume capacity in bytes",
            unit="bytes",
            callbacks=[self._get_volume_capacity_used_values]
        )

    # Callback methods for observable gauges
    def _get_array_status_values(self, options):
        """Callback to provide current array status values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('array_status', []):
            yield observation

    def _get_array_capacity_total_values(self, options):
        """Callback to provide current array total capacity values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('array_capacity_total', []):
            yield observation

    def _get_array_capacity_used_values(self, options):
        """Callback to provide current array used capacity values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('array_capacity_used', []):
            yield observation

    def _get_hardware_status_values(self, options):
        """Callback to provide current hardware status values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('hardware_status', []):
            yield observation

    def _get_drive_status_values(self, options):
        """Callback to provide current drive status values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('drive_status', []):
            yield observation

    def _get_controller_status_values(self, options):
        """Callback to provide current controller status values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('controller_status', []):
            yield observation

    def _get_volume_status_values(self, options):
        """Callback to provide current volume status values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('volume_status', []):
            yield observation

    def _get_volume_capacity_total_values(self, options):
        """Callback to provide current volume total capacity values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('volume_capacity_total', []):
            yield observation

    def _get_volume_capacity_used_values(self, options):
        """Callback to provide current volume used capacity values."""
        from opentelemetry.metrics import Observation
        for observation in self._gauge_values.get('volume_capacity_used', []):
            yield observation

    def report_arrays(self, arrays: List[Dict[str, Any]]):
        """Report array metrics."""
        with self.tracer.start_as_current_span("report_arrays") as span:
            span.set_attribute("arrays.count", len(arrays))

            # Clear previous values and store new ones
            from opentelemetry.metrics import Observation
            self._gauge_values['array_status'] = []
            self._gauge_values['array_capacity_total'] = []
            self._gauge_values['array_capacity_used'] = []

            for array in arrays:
                array_name = array.get('name', 'unknown')
                array_id = array.get('id', 'unknown')
                model = array.get('model', 'unknown')
                version = array.get('version', 'unknown')

                # Array status (assume healthy if no status field)
                status = 1 if array.get('status', 'healthy') == 'healthy' else 0

                attributes = {
                    "array_name": array_name,
                    "array_id": array_id,
                    "model": model,
                    "version": version
                }

                # Store current status value
                self._gauge_values['array_status'].append(
                    Observation(value=status, attributes=attributes)
                )

                # Capacity metrics
                space = array.get('space', {})
                if space:
                    total_capacity = space.get('total_capacity', 0)
                    used_capacity = space.get('used_capacity', 0)

                    capacity_attributes = {
                        "array_name": array_name,
                        "array_id": array_id
                    }

                    if total_capacity > 0:
                        self._gauge_values['array_capacity_total'].append(
                            Observation(value=total_capacity, attributes=capacity_attributes)
                        )

                    if used_capacity > 0:
                        self._gauge_values['array_capacity_used'].append(
                            Observation(value=used_capacity, attributes=capacity_attributes)
                        )
    
    def report_alerts(self, alerts: List[Dict[str, Any]]):
        """Report alert metrics."""
        with self.tracer.start_as_current_span("report_alerts") as span:
            span.set_attribute("alerts.count", len(alerts))

            # Count alerts by array, severity, and state
            alert_counts = {}

            for alert in alerts:
                array_name = alert.get('array', {}).get('name', 'unknown')
                array_id = alert.get('array', {}).get('id', 'unknown')
                severity = alert.get('severity', 'unknown')
                state = alert.get('state', 'unknown')

                key = (array_name, array_id, severity, state)
                alert_counts[key] = alert_counts.get(key, 0) + 1

            # Set metrics
            for (array_name, array_id, severity, state), count in alert_counts.items():
                attributes = {
                    "array_name": array_name,
                    "array_id": array_id,
                    "severity": severity,
                    "state": state
                }
                self.alert_count_counter.add(count, attributes)
    
    def report_hardware(self, hardware: List[Dict[str, Any]]):
        """Report hardware metrics."""
        with self.tracer.start_as_current_span("report_hardware") as span:
            span.set_attribute("hardware.count", len(hardware))

            # Clear previous values and store new ones
            from opentelemetry.metrics import Observation
            self._gauge_values['hardware_status'] = []

            for hw in hardware:
                array_name = hw.get('array', {}).get('name', 'unknown')
                array_id = hw.get('array', {}).get('id', 'unknown')
                component_type = hw.get('type', 'unknown')
                component_name = hw.get('name', 'unknown')

                # Hardware status
                status = 1 if hw.get('status', 'healthy') == 'healthy' else 0

                attributes = {
                    "array_name": array_name,
                    "array_id": array_id,
                    "component_type": component_type,
                    "component_name": component_name
                }

                # Store current status value
                self._gauge_values['hardware_status'].append(
                    Observation(value=status, attributes=attributes)
                )
    
    def report_drives(self, drives: List[Dict[str, Any]]):
        """Report drive metrics."""
        with self.tracer.start_as_current_span("report_drives") as span:
            span.set_attribute("drives.count", len(drives))

            # Clear previous values and store new ones
            from opentelemetry.metrics import Observation
            self._gauge_values['drive_status'] = []

            for drive in drives:
                array_name = drive.get('array', {}).get('name', 'unknown')
                array_id = drive.get('array', {}).get('id', 'unknown')
                drive_name = drive.get('name', 'unknown')
                drive_type = drive.get('type', 'unknown')

                # Drive status
                status = 1 if drive.get('status', 'healthy') == 'healthy' else 0

                attributes = {
                    "array_name": array_name,
                    "array_id": array_id,
                    "drive_name": drive_name,
                    "drive_type": drive_type
                }

                # Store current status value
                self._gauge_values['drive_status'].append(
                    Observation(value=status, attributes=attributes)
                )
    
    def report_controllers(self, controllers: List[Dict[str, Any]]):
        """Report controller metrics."""
        with self.tracer.start_as_current_span("report_controllers") as span:
            span.set_attribute("controllers.count", len(controllers))

            # Clear previous values and store new ones
            from opentelemetry.metrics import Observation
            self._gauge_values['controller_status'] = []

            for controller in controllers:
                array_name = controller.get('array', {}).get('name', 'unknown')
                array_id = controller.get('array', {}).get('id', 'unknown')
                controller_name = controller.get('name', 'unknown')

                # Controller status
                status = 1 if controller.get('status', 'healthy') == 'healthy' else 0

                attributes = {
                    "array_name": array_name,
                    "array_id": array_id,
                    "controller_name": controller_name
                }

                # Store current status value
                self._gauge_values['controller_status'].append(
                    Observation(value=status, attributes=attributes)
                )

    def report_volumes(self, volumes: List[Dict[str, Any]]):
        """Report volume metrics."""
        with self.tracer.start_as_current_span("report_volumes") as span:
            span.set_attribute("volumes.count", len(volumes))

            # Clear previous values and store new ones
            from opentelemetry.metrics import Observation
            self._gauge_values['volume_status'] = []
            self._gauge_values['volume_capacity_total'] = []
            self._gauge_values['volume_capacity_used'] = []

            for volume in volumes:
                array_name = volume.get('array', {}).get('name', 'unknown')
                volume_name = volume.get('name', 'unknown')

                # Volume status (1 for healthy, 0 for unhealthy)
                status = 1 if volume.get('status') == 'healthy' else 0

                # Volume capacity metrics
                space = volume.get('space', {})
                total_capacity = space.get('total_physical', 0)
                used_capacity = space.get('unique', 0)  # Unique space used by this volume

                # Common attributes for all volume metrics
                volume_attributes = {
                    "array_name": array_name,
                    "volume_name": volume_name,
                    "volume_id": volume.get('id', 'unknown')
                }

                # Store current status value
                self._gauge_values['volume_status'].append(
                    Observation(value=status, attributes=volume_attributes)
                )

                if total_capacity > 0:
                    self._gauge_values['volume_capacity_total'].append(
                        Observation(value=total_capacity, attributes=volume_attributes)
                    )

                if used_capacity > 0:
                    self._gauge_values['volume_capacity_used'].append(
                        Observation(value=used_capacity, attributes=volume_attributes)
                    )

    def report_health_summary(self, health_data: Dict[str, Any]):
        """Report health summary with traces."""
        with self.tracer.start_as_current_span("health_collection_summary") as span:
            # Add summary attributes to the span
            span.set_attribute("arrays.count", len(health_data.get('arrays', [])))
            span.set_attribute("alerts.count", len(health_data.get('alerts', [])))
            span.set_attribute("controllers.count", len(health_data.get('controllers', [])))
            span.set_attribute("drives.count", len(health_data.get('drives', [])))
            span.set_attribute("hardware.count", len(health_data.get('hardware', [])))
            span.set_attribute("volumes.count", len(health_data.get('volumes', [])))
            span.set_attribute("collection.timestamp", health_data.get('timestamp', ''))

            # Add events for significant findings
            alerts = health_data.get('alerts', [])
            critical_alerts = [a for a in alerts if a.get('severity') == 'critical']
            if critical_alerts:
                span.add_event(
                    "critical_alerts_found",
                    {"count": len(critical_alerts)}
                )

            logger.info(f"Health summary reported via OTLP: {len(health_data.get('arrays', []))} arrays, "
                       f"{len(alerts)} alerts, {len(critical_alerts)} critical")

    def force_flush(self):
        """Force flush all pending telemetry data."""
        try:
            # Force flush metrics
            meter_provider = metrics.get_meter_provider()
            if hasattr(meter_provider, 'force_flush'):
                meter_provider.force_flush(timeout_millis=5000)

            # Force flush traces
            trace_provider = trace.get_tracer_provider()
            if hasattr(trace_provider, 'force_flush'):
                trace_provider.force_flush(timeout_millis=5000)

            logger.info("Successfully flushed telemetry data to OTLP endpoint")
        except Exception as e:
            logger.error(f"Error flushing telemetry data: {e}")


class LogReporter:
    """Simple log-based reporter for alerts and health data."""

    def __init__(self, log_level: str = "INFO"):
        """
        Initialize the log reporter.

        Args:
            log_level: Log level for health reports
        """
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.logger = logging.getLogger("purestorage.health")
    
    def report_alerts(self, alerts: List[Dict[str, Any]]):
        """Report alerts as structured logs."""
        if not alerts:
            return

        for alert in alerts:
            severity = alert.get('severity', 'unknown')
            array_name = alert.get('array', {}).get('name', 'unknown')

            # Log at appropriate level based on severity
            log_level = logging.ERROR if severity == 'critical' else logging.WARNING

            self.logger.log(
                log_level,
                f"PureStorage Alert: {alert.get('summary', 'Unknown alert')}",
                extra={
                    'alert_id': alert.get('id'),
                    'severity': severity,
                    'state': alert.get('state'),
                    'array_name': array_name,
                    'array_id': alert.get('array', {}).get('id'),
                    'component_type': alert.get('component_type'),
                    'component_name': alert.get('component_name'),
                    'category': alert.get('category'),
                    'description': alert.get('description'),
                    'actual': alert.get('actual'),
                    'expected': alert.get('expected'),
                    'created': alert.get('created'),
                    'updated': alert.get('updated')
                }
            )
    
    def report_health_summary(self, health_summary: Dict[str, Any]):
        """Report health summary as a structured log."""
        arrays_count = len(health_summary.get('arrays', []))
        alerts_count = len(health_summary.get('alerts', []))
        controllers_count = len(health_summary.get('controllers', []))
        drives_count = len(health_summary.get('drives', []))
        hardware_count = len(health_summary.get('hardware', []))
        volumes_count = len(health_summary.get('volumes', []))

        self.logger.log(
            self.log_level,
            f"PureStorage Health Summary: {arrays_count} arrays, {alerts_count} alerts, "
            f"{controllers_count} controllers, {drives_count} drives, "
            f"{hardware_count} hardware components, {volumes_count} volumes",
            extra={
                'timestamp': health_summary.get('timestamp'),
                'arrays_count': arrays_count,
                'alerts_count': alerts_count,
                'controllers_count': controllers_count,
                'drives_count': drives_count,
                'hardware_count': hardware_count,
                'volumes_count': volumes_count,
                'collection_type': 'health_summary'
            }
        )


def main():
    """Test the reporters."""
    # Configure logging
    logging.basicConfig(level=logging.INFO)

    # Test data
    test_arrays = [
        {
            'name': 'test-array-1',
            'id': 'array-123',
            'model': 'FlashArray//X',
            'version': '6.4.0',
            'status': 'healthy',
            'space': {
                'total_physical': 1000000000000,
                'total_used': 500000000000
            }
        }
    ]

    test_alerts = [
        {
            'id': 'alert-123',
            'severity': 'critical',
            'state': 'open',
            'summary': 'Test alert',
            'description': 'This is a test alert',
            'array': {'name': 'test-array-1', 'id': 'array-123'},
            'component_type': 'drive',
            'component_name': 'drive1',
            'category': 'hardware',
            'created': int(time.time() * 1000)
        }
    ]

    test_health_data = {
        'timestamp': datetime.utcnow().isoformat(),
        'arrays': test_arrays,
        'alerts': test_alerts,
        'controllers': [],
        'drives': [],
        'hardware': [],
        'volumes': []
    }

    # Test OTLP reporter (would need actual OTLP endpoint)
    print("Testing OTLP reporter...")
    try:
        otlp = OTLPReporter("http://localhost:4317")  # Default OTLP gRPC endpoint
        otlp.report_arrays(test_arrays)
        otlp.report_alerts(test_alerts)
        otlp.report_health_summary(test_health_data)
        otlp.force_flush()
        print("✓ OTLP reporter test completed")
    except Exception as e:
        print(f"✗ OTLP reporter test failed: {e}")

    # Test log reporter
    print("Testing log reporter...")
    try:
        log_reporter = LogReporter()
        log_reporter.report_alerts(test_alerts)
        log_reporter.report_health_summary(test_health_data)
        print("✓ Log reporter test completed")
    except Exception as e:
        print(f"✗ Log reporter test failed: {e}")

    print("Reporter tests completed")


if __name__ == "__main__":
    main()
