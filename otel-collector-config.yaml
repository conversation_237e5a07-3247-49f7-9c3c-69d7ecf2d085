# OpenTelemetry Collector Configuration
# This is an optional configuration for local testing with OTLP

receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  
  resource:
    attributes:
      - key: environment
        value: "development"
        action: upsert

exporters:
  # Export to console for debugging
  logging:
    loglevel: info
  
  # Export metrics to Prometheus
  prometheus:
    endpoint: "0.0.0.0:8889"
    namespace: purestorage
    const_labels:
      environment: development
  
  # Example: Export to external OTLP endpoint
  # otlp/external:
  #   endpoint: "https://your-otlp-endpoint:4317"
  #   headers:
  #     authorization: "Bearer your-token"
  
  # Example: Export to <PERSON><PERSON><PERSON> for traces
  # jaeger:
  #   endpoint: "http://jaeger:14250"
  #   tls:
  #     insecure: true

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch, resource]
      exporters: [logging]
    
    metrics:
      receivers: [otlp]
      processors: [batch, resource]
      exporters: [logging, prometheus]
    
    logs:
      receivers: [otlp]
      processors: [batch, resource]
      exporters: [logging]

  extensions: []
