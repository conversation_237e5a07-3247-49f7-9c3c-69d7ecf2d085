# PureStorage Health Monitor - Nomad Deployment

This document provides instructions for deploying the PureStorage Health Monitor using HashiCorp Nomad.

## Prerequisites

- HashiCorp Nomad cluster (v1.4+)
- <PERSON><PERSON>Corp Consul for configuration management
- Docker runtime on Nomad clients
- Access to build and push Docker images

## Quick Start

1. **Build the Docker image:**
   ```bash
   docker build -t purestorage-monitor:latest .
   ```

2. **Configure Consul KV pairs:**
   ```bash
   # PureStorage API configuration
   consul kv put purestorage/app_id "pure1:apikey:your-app-id"
   consul kv put purestorage/private_key_passphrase "your-passphrase"
   
   # OTLP configuration
   consul kv put purestorage/otlp_endpoint "https://your-otlp-endpoint:4317"
   consul kv put purestorage/otlp_username "your-username"
   consul kv put purestorage/otlp_password "your-password"
   
   # SSL certificates
   consul kv put purestorage/ssl/private_key @private.pem
   consul kv put purestorage/ssl/public_key @public.pem
   ```

3. **Deploy using the provided script:**
   ```bash
   chmod +x deploy-nomad.sh
   ./deploy-nomad.sh
   ```

   Or deploy manually:
   ```bash
   nomad job validate purestorage-monitor.nomad
   nomad job run purestorage-monitor.nomad
   ```

## Configuration

### Consul KV Structure

The Nomad job uses Consul KV for configuration management. Set the following keys:

| Key | Description | Example |
|-----|-------------|---------|
| `purestorage/app_id` | PureStorage API App ID | `pure1:apikey:cajETP0O7YI55zQx` |
| `purestorage/private_key_passphrase` | Private key passphrase | `pure1` |
| `purestorage/otlp_endpoint` | OTLP endpoint URL | `https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp` |
| `purestorage/otlp_username` | OTLP username | `533612` |
| `purestorage/otlp_password` | OTLP password/token | `glc_eyJvIjoiMTM2...` |
| `purestorage/ssl/private_key` | SSL private key content | `-----BEGIN PRIVATE KEY-----...` |
| `purestorage/ssl/public_key` | SSL public key content | `-----BEGIN PUBLIC KEY-----...` |

### Job Configuration

The Nomad job specification includes:

- **Resource allocation**: 250 MHz CPU, 512 MB RAM
- **Restart policy**: 3 attempts over 30 minutes
- **Health checks**: Authentication test every 5 minutes
- **Logging**: JSON file driver with rotation
- **Volumes**: Persistent log storage

### Customization

To customize the deployment, modify `purestorage-monitor.nomad`:

1. **Resource limits**: Adjust CPU/memory in the `resources` block
2. **Collection interval**: Modify in the config template
3. **Datacenters**: Change the `datacenters` list
4. **Constraints**: Add node constraints as needed

## Operations

### Monitoring

```bash
# Check job status
nomad job status purestorage-monitor

# View allocations
nomad job allocs purestorage-monitor

# Follow logs
nomad alloc logs -f <allocation-id>

# Check service health
nomad service info purestorage-monitor
```

### Scaling

```bash
# Scale to multiple instances
nomad job scale purestorage-monitor 2

# Scale back to single instance
nomad job scale purestorage-monitor 1
```

### Updates

```bash
# Update configuration in Consul KV
consul kv put purestorage/otlp_endpoint "new-endpoint"

# Restart to pick up changes
nomad job restart purestorage-monitor

# Deploy new version
nomad job run purestorage-monitor.nomad
```

### Troubleshooting

1. **Authentication failures**:
   ```bash
   # Check SSL keys in Consul
   consul kv get purestorage/ssl/private_key
   
   # Test authentication manually
   nomad alloc exec <alloc-id> python -m src.main --test-auth
   ```

2. **OTLP connection issues**:
   ```bash
   # Verify endpoint configuration
   consul kv get purestorage/otlp_endpoint
   
   # Check network connectivity
   nomad alloc exec <alloc-id> curl -v https://your-otlp-endpoint
   ```

3. **Resource constraints**:
   ```bash
   # Check resource usage
   nomad alloc status <allocation-id>
   
   # View detailed logs
   nomad alloc logs -stderr <allocation-id>
   ```

## Security Considerations

- **SSL Keys**: Stored securely in Consul KV with appropriate ACLs
- **Secrets**: Use Consul KV encryption at rest
- **Network**: Consider using Consul Connect for service mesh
- **Access**: Implement Nomad ACLs for job management

## High Availability

For production deployments:

1. **Multiple instances**: Scale the job across different nodes
2. **Anti-affinity**: Add spread constraints to avoid single points of failure
3. **Monitoring**: Set up alerts for job failures
4. **Backup**: Regular backup of Consul KV data

Example anti-affinity constraint:
```hcl
constraint {
  operator  = "distinct_hosts"
  value     = "true"
}
```

## Integration with Existing Infrastructure

- **Service Discovery**: The job registers with Consul for service discovery
- **Load Balancing**: Use Consul Connect or external load balancers
- **Monitoring**: Metrics are sent to your configured OTLP endpoint
- **Logging**: Structured logs available through Nomad's logging drivers
