#!/bin/bash

# PureStorage Health Monitor - Local Build Test Script
# This script tests the Docker build process locally

set -e

echo "🔨 PureStorage Health Monitor - Local Build Test"
echo "=============================================="

# Configuration
IMAGE_NAME="obs-purestorage"
IMAGE_TAG="test-$(date +%s)"
FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"

echo "Building image: $FULL_IMAGE_NAME"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up..."
    if docker images -q "$FULL_IMAGE_NAME" >/dev/null 2>&1; then
        echo "Removing test image: $FULL_IMAGE_NAME"
        docker rmi "$FULL_IMAGE_NAME" >/dev/null 2>&1 || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Check if Docker is available
if ! command -v docker >/dev/null 2>&1; then
    echo "❌ Error: Docker not found!"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Error: Docker daemon is not running!"
    echo "Please start Docker and try again."
    exit 1
fi

echo "✅ Docker is available and running"

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    echo "❌ Error: Dockerfile not found!"
    echo "Please ensure you're running this script from the project root directory."
    exit 1
fi

echo "✅ Dockerfile found"

# Check if source files exist
echo ""
echo "🔍 Checking source files..."

if [ ! -f "requirements.txt" ]; then
    echo "❌ Error: requirements.txt not found!"
    exit 1
fi
echo "✅ requirements.txt found"

if [ ! -d "src" ]; then
    echo "❌ Error: src directory not found!"
    exit 1
fi
echo "✅ src directory found"

# Check for SSL keys (optional)
if [ -f "private.pem" ] && [ -f "public.pem" ]; then
    echo "✅ SSL keys found"
else
    echo "⚠️  SSL keys not found (private.pem, public.pem)"
    echo "   This is OK for build testing, but required for runtime"
fi

# Build the Docker image
echo ""
echo "🔨 Building Docker image..."
echo "Command: docker build -t $FULL_IMAGE_NAME ."
echo ""

if docker build -t "$FULL_IMAGE_NAME" .; then
    echo ""
    echo "✅ Docker build successful!"
else
    echo ""
    echo "❌ Docker build failed!"
    exit 1
fi

# Get image information
echo ""
echo "📊 Image Information:"
docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# Test basic container functionality
echo ""
echo "🧪 Testing container functionality..."

# Test 1: Check if container starts
echo "Test 1: Container startup test..."
if docker run --rm "$FULL_IMAGE_NAME" python -c "print('Container starts successfully')"; then
    echo "✅ Container startup test passed"
else
    echo "❌ Container startup test failed"
    exit 1
fi

# Test 2: Check if Python modules can be imported
echo ""
echo "Test 2: Python module import test..."
if docker run --rm "$FULL_IMAGE_NAME" python -c "
import sys
sys.path.append('/app')
try:
    import src.config
    import src.jwt_generator
    import src.oauth_client
    import src.purestorage_client
    import src.reporters
    import src.main
    print('All modules imported successfully')
except ImportError as e:
    print(f'Import error: {e}')
    sys.exit(1)
"; then
    echo "✅ Python module import test passed"
else
    echo "❌ Python module import test failed"
    exit 1
fi

# Test 3: Check if help command works
echo ""
echo "Test 3: Application help test..."
if docker run --rm "$FULL_IMAGE_NAME" python -m src.main --help >/dev/null 2>&1; then
    echo "✅ Application help test passed"
else
    echo "❌ Application help test failed"
    exit 1
fi

# Show final results
echo ""
echo "🎉 All tests passed!"
echo ""
echo "📋 Build Summary:"
echo "  Image Name: $FULL_IMAGE_NAME"
echo "  Image Size: $(docker images "$FULL_IMAGE_NAME" --format "{{.Size}}")"
echo "  Build Time: $(date)"
echo ""
echo "🚀 Your Docker image is ready for deployment!"
echo ""
echo "💡 Next steps:"
echo "  1. Test with real configuration: docker run --rm -v \$(pwd)/config.yaml:/app/config.yaml $FULL_IMAGE_NAME"
echo "  2. Push to registry: docker tag $FULL_IMAGE_NAME your-registry/obs-purestorage:latest"
echo "  3. Deploy to Nomad: ./deploy-pipeline.sh"
echo ""

# Ask if user wants to keep the image
read -p "Do you want to keep the test image? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Disable cleanup
    trap - EXIT
    echo "✅ Test image preserved: $FULL_IMAGE_NAME"
    echo "   To remove later: docker rmi $FULL_IMAGE_NAME"
else
    echo "🧹 Test image will be removed automatically"
fi
