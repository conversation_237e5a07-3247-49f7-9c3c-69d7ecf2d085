#!/usr/bin/env python3
"""
Quick test to verify JWT generation with actual keys.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from jwt_generator import JWTGenerator
    
    print("Testing JWT generation with your keys...")
    
    # Test with the provided keys and app ID
    jwt_gen = JWTGenerator(
        private_key_path="private.pem",
        app_id="pure1:apikey:cajETP0O7YI55zQx"
    )
    
    # Generate a token
    token = jwt_gen.generate_token(expiry_days=30)
    print(f"✓ JWT token generated successfully!")
    print(f"  Token preview: {token[:50]}...")
    
    # Get token info
    info = jwt_gen.get_token_info(token)
    print(f"  Issuer: {info.get('issuer')}")
    print(f"  Expires at: {info.get('expires_at')}")
    print(f"  Is expired: {info.get('is_expired')}")
    
    # Validate token
    try:
        decoded = jwt_gen.validate_token(token, "public.pem")
        print(f"✓ Token validation successful!")
        print(f"  Subject: {decoded.get('sub')}")
        print(f"  Audience: {decoded.get('aud')}")
    except Exception as e:
        print(f"✗ Token validation failed: {e}")
    
    print("\n✓ JWT generation is working correctly!")
    print("You can now proceed with the full setup.")
    
except Exception as e:
    print(f"✗ Error: {e}")
    print("\nPlease ensure:")
    print("1. private.pem and public.pem are in the current directory")
    print("2. The keys are valid PEM format")
    print("3. Required dependencies are installed: pip install cryptography PyJWT")
