"""
PureStorage Health Monitor - Main Application

This is the main application that orchestrates token generation, API queries,
and data reporting with proper error handling and scheduling.
"""

import sys
import time
import signal
import schedule
from datetime import datetime, timedelta
from typing import Optional
import logging
import argparse
from pathlib import Path

# Import our modules
from .config import Config, load_config, setup_logging, create_sample_config
from .jwt_generator import J<PERSON><PERSON><PERSON>enerator
from .oauth_client import OAuthClient
from .purestorage_client import PureStorageClient
from .reporters import OTLPReporter, LogReporter

logger = logging.getLogger(__name__)


class PureStorageHealthMonitor:
    """Main health monitoring application."""
    
    def __init__(self, config: Config):
        """
        Initialize the health monitor.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.running = False
        
        # Initialize components
        self.jwt_generator = None
        self.oauth_client = None
        self.ps_client = None
        self.otlp_reporter = None
        self.log_reporter = None
        
        self._setup_components()
        self._setup_signal_handlers()
    
    def _setup_components(self):
        """Set up all components."""
        try:
            # JWT Generator
            self.jwt_generator = JWTGenerator(
                private_key_path=self.config.purestorage.private_key_path,
                app_id=self.config.purestorage.app_id,
                passphrase=self.config.purestorage.private_key_passphrase
            )
            logger.info("JWT generator initialized")
            
            # OAuth Client
            logger.info(f"Initializing OAuth client with proxy: {self.config.purestorage.proxy_url}")
            self.oauth_client = OAuthClient(
                base_url=self.config.purestorage.api_base_url,
                proxy_url=self.config.purestorage.proxy_url
            )
            logger.info("OAuth client initialized")
            
            # PureStorage Client
            self.ps_client = PureStorageClient(
                oauth_client=self.oauth_client,
                base_url=self.config.purestorage.api_base_url
            )
            logger.info("PureStorage client initialized")
            
            # OTLP Reporter
            if self.config.otlp and self.config.otlp.enabled:
                logger.info(f"Initializing OTLP reporter with proxy: {self.config.purestorage.proxy_url}")
                self.otlp_reporter = OTLPReporter(
                    otlp_endpoint=self.config.otlp.endpoint,
                    service_name=self.config.otlp.service_name,
                    username=getattr(self.config.otlp, 'username', None),
                    password=getattr(self.config.otlp, 'password', None),
                    proxy_url=self.config.purestorage.proxy_url
                )
                logger.info("OTLP reporter initialized")

            # Log Reporter
            if self.config.log_reporting and self.config.log_reporting.enabled:
                self.log_reporter = LogReporter(
                    log_level=self.config.log_reporting.level
                )
                logger.info("Log reporter initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down gracefully...")
            self.stop()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def authenticate(self) -> bool:
        """
        Authenticate with the PureStorage API.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            logger.info("Authenticating with PureStorage API...")
            
            # Generate JWT token
            jwt_token = self.jwt_generator.generate_token(
                expiry_days=self.config.purestorage.token_expiry_days
            )
            
            # Exchange for access token
            self.oauth_client.exchange_token(jwt_token)
            
            logger.info("Authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def collect_health_data(self) -> Optional[dict]:
        """
        Collect health data from PureStorage API.
        
        Returns:
            Health data dictionary or None if collection failed
        """
        try:
            logger.info("Starting health data collection...")
            
            # Ensure we have a valid token
            if not self.oauth_client.is_token_valid():
                if not self.authenticate():
                    logger.error("Cannot collect data without valid authentication")
                    return None
            
            # Collect comprehensive health data
            health_data = {
                'timestamp': datetime.utcnow().isoformat(),
                'arrays': [],
                'alerts': [],
                'controllers': [],
                'drives': [],
                'hardware': [],
                'volumes': []
            }
            
            # Get arrays
            health_data['arrays'] = self.ps_client.get_arrays(
                limit=self.config.monitoring.max_arrays
            )
            
            # Get alerts
            for severity in self.config.monitoring.alert_severities:
                for state in self.config.monitoring.alert_states:
                    alerts = self.ps_client.get_alerts(
                        limit=self.config.monitoring.max_alerts // 
                              (len(self.config.monitoring.alert_severities) * 
                               len(self.config.monitoring.alert_states)),
                        severity=severity,
                        state=state
                    )
                    health_data['alerts'].extend(alerts)
            
            # Get controllers
            health_data['controllers'] = self.ps_client.get_controllers(
                limit=self.config.monitoring.max_controllers
            )
            
            # Get drives
            health_data['drives'] = self.ps_client.get_drives(
                limit=self.config.monitoring.max_drives
            )
            
            # Get hardware
            health_data['hardware'] = self.ps_client.get_hardware(
                limit=self.config.monitoring.max_hardware
            )
            
            # Get volumes (sample)
            health_data['volumes'] = self.ps_client.get_volumes(
                limit=self.config.monitoring.max_volumes
            )
            
            logger.info(f"Health data collection completed: "
                       f"{len(health_data['arrays'])} arrays, "
                       f"{len(health_data['alerts'])} alerts, "
                       f"{len(health_data['controllers'])} controllers, "
                       f"{len(health_data['drives'])} drives, "
                       f"{len(health_data['hardware'])} hardware components, "
                       f"{len(health_data['volumes'])} volumes")
            
            return health_data
            
        except Exception as e:
            logger.error(f"Health data collection failed: {e}")
            return None
    
    def report_data(self, health_data: dict):
        """
        Report health data to configured backends.
        
        Args:
            health_data: Health data dictionary
        """
        try:
            # Report to OTLP
            if self.otlp_reporter:
                logger.info("Reporting telemetry to OTLP endpoint...")
                self.otlp_reporter.report_arrays(health_data['arrays'])
                self.otlp_reporter.report_alerts(health_data['alerts'])
                self.otlp_reporter.report_controllers(health_data['controllers'])
                self.otlp_reporter.report_drives(health_data['drives'])
                self.otlp_reporter.report_hardware(health_data['hardware'])
                self.otlp_reporter.report_volumes(health_data['volumes'])
                self.otlp_reporter.report_health_summary(health_data)
                self.otlp_reporter.force_flush()
                logger.info("OTLP reporting completed")

            # Report to logs
            if self.log_reporter:
                logger.info("Reporting to structured logs...")
                self.log_reporter.report_alerts(health_data['alerts'])
                self.log_reporter.report_health_summary(health_data)
                logger.info("Log reporting completed")

        except Exception as e:
            logger.error(f"Data reporting failed: {e}")
    
    def run_collection_cycle(self):
        """Run a single collection cycle."""
        logger.info("Starting collection cycle...")
        
        try:
            # Collect health data
            health_data = self.collect_health_data()
            
            if health_data:
                # Report data
                self.report_data(health_data)
                logger.info("Collection cycle completed successfully")
            else:
                logger.warning("Collection cycle completed with no data")
                
        except Exception as e:
            logger.error(f"Collection cycle failed: {e}")
    
    def start(self):
        """Start the health monitor."""
        logger.info("Starting PureStorage Health Monitor...")
        
        # Initial authentication
        if not self.authenticate():
            logger.error("Initial authentication failed, exiting")
            return False
        
        # Schedule collection
        schedule.every(self.config.monitoring.collection_interval).seconds.do(
            self.run_collection_cycle
        )
        
        # Run initial collection
        self.run_collection_cycle()
        
        # Main loop
        self.running = True
        logger.info(f"Health monitor started, collecting every {self.config.monitoring.collection_interval} seconds")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt, shutting down...")
                break
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(5)  # Brief pause before continuing
        
        logger.info("Health monitor stopped")
        return True
    
    def stop(self):
        """Stop the health monitor."""
        self.running = False
    
    def run_once(self):
        """Run a single collection cycle and exit."""
        logger.info("Running single collection cycle...")

        if not self.authenticate():
            logger.error("Authentication failed")
            return False

        self.run_collection_cycle()
        return True

    def run_continuous(self):
        """Run continuous monitoring with simple sleep loop."""
        logger.info("Starting continuous monitoring...")
        logger.info(f"Collection interval: {self.config.monitoring.collection_interval} seconds")

        # Initial authentication
        if not self.authenticate():
            logger.error("Initial authentication failed")
            return False

        while True:
            try:
                self.run_collection_cycle()
                logger.info(f"Sleeping for {self.config.monitoring.collection_interval} seconds...")
                time.sleep(self.config.monitoring.collection_interval)
            except KeyboardInterrupt:
                logger.info("Received interrupt signal, shutting down...")
                break
            except Exception as e:
                logger.error(f"Error in continuous monitoring: {e}")
                logger.info(f"Sleeping for {self.config.monitoring.collection_interval} seconds before retry...")
                time.sleep(self.config.monitoring.collection_interval)

        logger.info("Continuous monitoring stopped")
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="PureStorage Health Monitor")
    parser.add_argument(
        "--config", "-c",
        type=str,
        default="config.yaml",
        help="Configuration file path"
    )
    parser.add_argument(
        "--create-config",
        action="store_true",
        help="Create a sample configuration file"
    )
    parser.add_argument(
        "--run-once",
        action="store_true",
        help="Run a single collection cycle and exit"
    )
    parser.add_argument(
        "--test-auth",
        action="store_true",
        help="Test authentication and exit"
    )
    parser.add_argument(
        "--continuous",
        action="store_true",
        help="Run continuous monitoring (simple loop)"
    )
    
    args = parser.parse_args()
    
    # Create sample config if requested
    if args.create_config:
        create_sample_config(args.config)
        return 0
    
    try:
        # Load configuration
        config = load_config(args.config)
        
        # Setup logging
        setup_logging(config.logging)
        
        # Create monitor
        monitor = PureStorageHealthMonitor(config)
        
        # Test authentication if requested
        if args.test_auth:
            if monitor.authenticate():
                logger.info("Authentication test successful")
                return 0
            else:
                logger.error("Authentication test failed")
                return 1
        
        # Run once if requested
        if args.run_once:
            if monitor.run_once():
                return 0
            else:
                return 1

        # Run continuous monitoring (simple loop)
        if args.continuous:
            if monitor.run_continuous():
                return 0
            else:
                return 1

        # Start continuous monitoring (default - with scheduler)
        if monitor.start():
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
