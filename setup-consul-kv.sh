#!/bin/bash

# PureStorage Health Monitor - Consul KV Setup Script

set -e

echo "🔑 PureStorage Health Monitor - Consul KV Setup"
echo "==============================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if consul command exists
if ! command_exists consul; then
    echo "❌ Error: Consul CLI not found!"
    echo "Please install Consul: https://www.consul.io/downloads"
    exit 1
fi

# Check if we can connect to Consul
if ! consul kv get -keys > /dev/null 2>&1; then
    echo "❌ Error: Cannot connect to Consul!"
    echo "Please ensure <PERSON> is running and accessible."
    exit 1
fi

echo "✅ Consul connection verified!"
echo ""

# Function to prompt for input with default
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            echo "This field is required."
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

# Function to prompt for sensitive input
prompt_sensitive() {
    local prompt="$1"
    local var_name="$2"
    
    read -s -p "$prompt: " input
    echo
    while [ -z "$input" ]; do
        echo "This field is required."
        read -s -p "$prompt: " input
        echo
    done
    
    eval "$var_name='$input'"
}

# Function to prompt for file input
prompt_file() {
    local prompt="$1"
    local var_name="$2"
    local default="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
    fi
    
    while [ ! -f "$input" ]; do
        echo "File not found: $input"
        read -p "$prompt: " input
    done
    
    eval "$var_name='$input'"
}

echo "📝 Please provide the following configuration values:"
echo ""

# PureStorage configuration
echo "🔧 PureStorage API Configuration:"
prompt_with_default "App ID" "pure1:apikey:cajETP0O7YI55zQx" APP_ID
prompt_sensitive "Private key passphrase" PASSPHRASE

echo ""

# OTLP configuration
echo "📊 OTLP Configuration:"
prompt_with_default "OTLP endpoint" "https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp" OTLP_ENDPOINT
prompt_with_default "OTLP username" "533612" OTLP_USERNAME
prompt_sensitive "OTLP password/token" OTLP_PASSWORD

echo ""

# SSL files
echo "🔐 SSL Certificate Files:"
prompt_file "Private key file path" PRIVATE_KEY_FILE "private.pem"
prompt_file "Public key file path" PUBLIC_KEY_FILE "public.pem"

echo ""
echo "🚀 Setting up Consul KV pairs..."

# Set PureStorage configuration
echo "Setting PureStorage configuration..."
consul kv put purestorage/app_id "$APP_ID"
consul kv put purestorage/private_key_passphrase "$PASSPHRASE"

# Set OTLP configuration
echo "Setting OTLP configuration..."
consul kv put purestorage/otlp_endpoint "$OTLP_ENDPOINT"
consul kv put purestorage/otlp_username "$OTLP_USERNAME"
consul kv put purestorage/otlp_password "$OTLP_PASSWORD"

# Set SSL certificates
echo "Setting SSL certificates..."
consul kv put purestorage/ssl/private_key @"$PRIVATE_KEY_FILE"
consul kv put purestorage/ssl/public_key @"$PUBLIC_KEY_FILE"

echo ""
echo "✅ Consul KV setup completed successfully!"
echo ""
echo "📋 Configured keys:"
echo "  - purestorage/app_id"
echo "  - purestorage/private_key_passphrase"
echo "  - purestorage/otlp_endpoint"
echo "  - purestorage/otlp_username"
echo "  - purestorage/otlp_password"
echo "  - purestorage/ssl/private_key"
echo "  - purestorage/ssl/public_key"
echo ""
echo "🔍 To verify the configuration:"
echo "  consul kv get -keys -separator='' purestorage/"
echo ""
echo "🚀 You can now deploy the Nomad job:"
echo "  ./deploy-nomad.sh"
echo ""
