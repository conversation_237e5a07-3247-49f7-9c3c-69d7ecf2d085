"""
JWT Token Generator for PureStorage API Authentication

This module handles the generation of JWT tokens using the private key
for authentication with the PureStorage Pure1 API.
"""

import jwt
import time
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class JWTGenerator:
    """Generates JWT tokens for PureStorage API authentication."""
    
    def __init__(self, private_key_path: str, app_id: str, passphrase: Optional[str] = None):
        """
        Initialize the JWT generator.
        
        Args:
            private_key_path: Path to the private key file
            app_id: PureStorage API application ID
            passphrase: Optional passphrase for encrypted private key
        """
        self.private_key_path = Path(private_key_path)
        self.app_id = app_id
        self.passphrase = passphrase.encode() if passphrase else None
        self._private_key = None
        self._load_private_key()
    
    def _load_private_key(self) -> None:
        """Load the private key from file."""
        try:
            with open(self.private_key_path, 'rb') as key_file:
                self._private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=self.passphrase
                )
            logger.info(f"Successfully loaded private key from {self.private_key_path}")
        except Exception as e:
            logger.error(f"Failed to load private key: {e}")
            raise
    
    def generate_token(self, expiry_days: int = 30) -> str:
        """
        Generate a JWT token for PureStorage API authentication.
        
        Args:
            expiry_days: Number of days until token expires (default: 30)
            
        Returns:
            JWT token string
        """
        now = datetime.utcnow()
        expiry = now + timedelta(days=expiry_days)
        
        # JWT payload according to PureStorage API requirements
        # Based on: https://www.codyhosterman.com/2020/02/generating-a-pure1-rest-jwt-with-python/
        payload = {
            'iss': self.app_id,  # Issuer - the API application ID
            'iat': int(now.timestamp()),  # Issued at time
            'exp': int(expiry.timestamp()),  # Expiration time
        }
        
        # Generate the JWT token
        try:
            token = jwt.encode(
                payload,
                self._private_key,
                algorithm='RS256'
            )
            
            logger.info(f"Generated JWT token expiring at {expiry}")
            return token
            
        except Exception as e:
            logger.error(f"Failed to generate JWT token: {e}")
            raise
    
    def validate_token(self, token: str, public_key_path: Optional[str] = None) -> dict:
        """
        Validate a JWT token (useful for testing).
        
        Args:
            token: JWT token to validate
            public_key_path: Optional path to public key for validation
            
        Returns:
            Decoded token payload
        """
        if public_key_path:
            with open(public_key_path, 'rb') as key_file:
                public_key = serialization.load_pem_public_key(key_file.read())
        else:
            # Extract public key from private key
            public_key = self._private_key.public_key()
        
        try:
            decoded = jwt.decode(
                token,
                public_key,
                algorithms=['RS256'],
                issuer=self.app_id
            )
            logger.info("Token validation successful")
            return decoded
            
        except jwt.ExpiredSignatureError:
            logger.error("Token has expired")
            raise
        except jwt.InvalidTokenError as e:
            logger.error(f"Token validation failed: {e}")
            raise
    
    def is_token_expired(self, token: str) -> bool:
        """
        Check if a token is expired without validating signature.
        
        Args:
            token: JWT token to check
            
        Returns:
            True if token is expired, False otherwise
        """
        try:
            # Decode without verification to check expiry
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get('exp', 0)
            return time.time() > exp_timestamp
        except Exception:
            return True  # Assume expired if we can't decode
    
    def get_token_info(self, token: str) -> dict:
        """
        Get token information without validation.
        
        Args:
            token: JWT token to inspect
            
        Returns:
            Token payload information
        """
        try:
            decoded = jwt.decode(token, options={"verify_signature": False})
            exp_timestamp = decoded.get('exp', 0)
            iat_timestamp = decoded.get('iat', 0)
            
            return {
                'issuer': decoded.get('iss'),
                'audience': decoded.get('aud'),
                'subject': decoded.get('sub'),
                'issued_at': datetime.fromtimestamp(iat_timestamp),
                'expires_at': datetime.fromtimestamp(exp_timestamp),
                'is_expired': time.time() > exp_timestamp
            }
        except Exception as e:
            logger.error(f"Failed to get token info: {e}")
            return {}


def main():
    """Test the JWT generator."""
    import os
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Test configuration
    private_key_path = "private.pem"
    app_id = "pure1:apikey:cajETP0O7YI55zQx"
    
    try:
        # Create JWT generator
        jwt_gen = JWTGenerator(private_key_path, app_id)
        
        # Generate a token
        token = jwt_gen.generate_token(expiry_days=30)
        print(f"Generated token: {token[:50]}...")
        
        # Get token info
        info = jwt_gen.get_token_info(token)
        print(f"Token info: {info}")
        
        # Validate token
        try:
            decoded = jwt_gen.validate_token(token, "public.pem")
            print(f"Token validation successful: {decoded}")
        except Exception as e:
            print(f"Token validation failed: {e}")
            
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
