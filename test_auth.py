#!/usr/bin/env python3
"""
Test script to verify Basic auth encoding for Grafana Cloud OTLP
"""

import base64

# Your credentials
instance_id = "533612"
token = "************************************************************************************************************************************************************"

# Create Basic auth string
credentials = f"{instance_id}:{token}"
encoded_credentials = base64.b64encode(credentials.encode()).decode()

print(f"Instance ID: {instance_id}")
print(f"Token: {token}")
print(f"Credentials string: {credentials}")
print(f"Base64 encoded: {encoded_credentials}")
print(f"Authorization header: Basic {encoded_credentials}")

# Test if this matches what we have in config
current_auth = "Basic ****************************************************************************************************************************************************************************************************************************"
expected_auth = f"Basic {encoded_credentials}"

print(f"\nCurrent auth in config: {current_auth}")
print(f"Expected auth: {expected_auth}")
print(f"Match: {current_auth == expected_auth}")

# Decode the current auth to see what it contains
try:
    current_encoded = current_auth.replace("Basic ", "")
    current_decoded = base64.b64decode(current_encoded).decode()
    print(f"Current decoded: {current_decoded}")
except Exception as e:
    print(f"Error decoding current auth: {e}")
