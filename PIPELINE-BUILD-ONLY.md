# Bitbucket Pipeline - Build and Push Only

## 🎯 Current Pipeline Focus

The pipeline is currently configured to focus on **building and pushing Docker images** to your Quay registry. Nomad deployment will be tackled separately later.

## 🚀 Current Workflow

### Automatic Triggers

| Branch/Event | Actions | Result |
|--------------|---------|--------|
| **main** | Build → Test → Docker Build → Push to Quay | Image tagged with commit hash + latest |
| **develop** | Build → Test → Docker Build → Push to Quay | Image tagged with commit hash |
| **Pull Requests** | Build → Test → Docker Build | Image built but not pushed |
| **Tags (v\*)** | Build → Test → Docker Build → Push to Quay | Image tagged with version + latest |

### Manual Triggers

| Custom Pipeline | Actions | Use Case |
|----------------|---------|----------|
| **build-and-push** | Build → Test → Docker Build → Push to Quay | Manual build and push |

## 📦 Image Tagging Strategy

| Source | Image Tags | Registry Location |
|--------|------------|-------------------|
| main branch | `{commit-hash}`, `latest` | `quay.education.nsw.gov.au/observability/purestorage-monitor` |
| develop branch | `{commit-hash}` | `quay.education.nsw.gov.au/observability/purestorage-monitor` |
| version tags | `{version}`, `latest` | `quay.education.nsw.gov.au/observability/purestorage-monitor` |
| pull requests | `{commit-hash}` | Local only (not pushed) |

## ⚙️ Required Bitbucket Variables

Make sure these are set in your repository settings:

```bash
# Required for Quay push
QUAY_USERNAME=your-quay-username
QUAY_PASSWORD=your-quay-password
```

## 🧪 Testing the Pipeline

### 1. Test Build Locally
```bash
# Test the Docker build process
./test-build.sh
```

### 2. Validate Pipeline Config
```bash
# Check pipeline configuration
./validate-pipeline.sh
```

### 3. Manual Pipeline Run
- Go to Bitbucket → Pipelines → Run pipeline
- Select "Custom: build-and-push"
- This will build and push without waiting for a commit

## 📋 What Each Step Does

### Step 1: Build and Test
- Sets up proxy configuration for your environment
- Installs Python 3, pip, and system dependencies
- Installs Python requirements from `requirements.txt`
- Runs basic syntax and import tests on all Python modules

### Step 2: Build Docker Image
- Sets up proxy environment variables
- Builds Docker image using your Dockerfile
- Tags image with commit hash and latest
- Saves image metadata for next step

### Step 3: Push to Quay
- Sets up proxy environment variables
- Logs into your internal Quay registry
- Rebuilds the image (since Docker images aren't passed between steps)
- Pushes both the commit-specific tag and latest tag

## 🔍 Monitoring Success

### Pipeline Success Indicators
- ✅ All steps show green checkmarks
- ✅ No error messages in logs
- ✅ "Successfully pushed" message appears

### Verify in Quay Registry
1. Go to `quay.education.nsw.gov.au`
2. Navigate to `observability/purestorage-monitor`
3. Check that new images appear with correct tags

### Check Image Details
```bash
# Pull and inspect the image
docker pull quay.education.nsw.gov.au/observability/purestorage-monitor:latest
docker inspect quay.education.nsw.gov.au/observability/purestorage-monitor:latest
```

## 🐛 Troubleshooting

### Common Issues

1. **Python/pip not found**
   - Fixed: Pipeline now installs Python 3 and pip
   - Creates symlinks for compatibility

2. **Proxy connection issues**
   - Fixed: Proxy configuration applied to all steps
   - Both apt and pip operations use proxy

3. **Quay login failures**
   - Check QUAY_USERNAME and QUAY_PASSWORD variables
   - Verify credentials work manually

4. **Docker build failures**
   - Check Dockerfile syntax
   - Verify all required files are present
   - Review build logs for specific errors

### Debug Commands

```bash
# Test Quay login manually
echo $QUAY_PASSWORD | docker login quay.education.nsw.gov.au -u $QUAY_USERNAME --password-stdin

# Test Docker build locally
docker build -t test-image .

# Check proxy settings
curl -I http://proxy.det.nsw.edu.au:80
```

## 🔄 Next Steps (Future)

Once build and push is working reliably:

1. **Enable Nomad deployment** by uncommenting deployment steps
2. **Set up Consul KV** configuration for the application
3. **Configure Nomad cluster** access from Bitbucket
4. **Test deployment pipeline** in staging environment

## 📝 Current Pipeline Status

- ✅ **Build and Test**: Configured with Python installation and proxy support
- ✅ **Docker Build**: Configured with proxy support
- ✅ **Push to Quay**: Configured for internal registry
- ⏸️ **Deploy to Nomad**: Disabled (to be enabled later)

## 🎯 Success Criteria

Your pipeline is working when:
- Code changes trigger automatic builds
- Images are successfully pushed to Quay
- No errors in pipeline logs
- Images are accessible from Quay registry

---

**Ready to test?** Commit your changes and push to trigger the first pipeline run!
